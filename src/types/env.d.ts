/// <reference types="vite/client" />

// 环境变量类型定义
interface ImportMetaEnv {
  // 应用基础配置
  readonly VITE_APP_TITLE: string;
  readonly VITE_APP_VERSION: string;

  // API 配置
  readonly VITE_API_BASE_URL: string;
  readonly VITE_API_TIMEOUT: string;

  // TUS 上传配置
  readonly VITE_TUS_ENDPOINT: string;
  readonly VITE_TUS_CHUNK_SIZE: string;
  readonly VITE_TUS_PARALLEL_UPLOADS: string;

  // 下载配置
  readonly VITE_DOWNLOAD_ENDPOINT: string;

  // 智能打包配置
  readonly VITE_SMART_PACKING_THRESHOLD: string;

  // 开发配置
  readonly VITE_ENABLE_MOCK: string;
  readonly VITE_ENABLE_DEBUG: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

// 环境变量工具类型
export type AppConfig = {
  app: {
    title: string;
    version: string;
  };
  api: {
    baseURL: string;
    timeout: number;
  };
  tus: {
    endpoint: string;
    chunkSize: number;
    parallelUploads: number;
  };
  download: {
    endpoint: string;
  };
  smartPacking: {
    threshold: number;
  };
  dev: {
    enableMock: boolean;
    enableDebug: boolean;
  };
};
