/**
 * 文件处理工具函数
 * 提供文件对象创建、路径处理等通用功能
 */

/**
 * 文件信息接口
 */
export interface FileInfo {
  /** 文件绝对路径 */
  path: string;
  /** 文件名 */
  name?: string;
  /** 相对路径（用于保持目录结构） */
  relativePath?: string;
  /** 是否为目录 */
  isDirectory?: boolean;
  /** 是否为空目录 */
  isEmpty?: boolean;
  /** 文件大小 */
  size?: number;
  /** 最后修改时间 */
  mtime?: number;
}

/**
 * 为空文件夹创建特殊的 File 对象
 *
 * @param fileInfo 文件信息对象
 * @returns 配置好的空文件夹 File 对象
 */
export function createEmptyFolderFile(fileInfo: FileInfo): File {
  // 提取文件夹名称（支持 Windows 和 Unix 路径分隔符）
  const folderName = fileInfo.name || fileInfo.path.split(/[/\\]/).pop() || "unknown";

  // 创建空文件夹的 File 对象
  const file = new File([], folderName, {
    type: "application/x-directory",
    lastModified: fileInfo.mtime || Date.now(),
  });

  // 添加路径信息到 File 对象
  Object.defineProperty(file, "path", {
    value: fileInfo.path,
    writable: false,
  });

  Object.defineProperty(file, "size", {
    value: 0,
    writable: false,
  });

  // 设置相对路径（如果提供）
  if (fileInfo.relativePath) {
    Object.defineProperty(file, "webkitRelativePath", {
      value: fileInfo.relativePath,
      writable: false,
    });
  }

  // 标记为空文件夹
  Object.defineProperty(file, "isFolder", {
    value: true,
    writable: false,
  });

  Object.defineProperty(file, "isEmpty", {
    value: true,
    writable: false,
  });

  return file;
}

/**
 * 检查文件信息是否表示空文件夹
 *
 * @param fileInfo 文件信息对象
 * @returns 是否为空文件夹
 */
export function isEmptyFolder(fileInfo: FileInfo): boolean {
  return Boolean(fileInfo.isDirectory && fileInfo.isEmpty);
}

/**
 * 从文件路径提取文件名（跨平台兼容）
 *
 * @param filePath 文件路径
 * @returns 文件名
 */
export function extractFileName(filePath: string): string {
  return filePath.split(/[/\\]/).pop() || "unknown";
}

/**
 * 检查相对路径是否包含目录结构
 *
 * @param relativePath 相对路径
 * @param fileName 文件名
 * @returns 是否包含目录结构
 */
export function hasPathStructure(relativePath?: string, fileName?: string): boolean {
  if (!relativePath || !fileName) return false;

  return relativePath !== fileName && (relativePath.includes("/") || relativePath.includes("\\"));
}

/**
 * 从文件路径创建 File 对象（Electron 环境专用）
 *
 * @param filePath 文件绝对路径
 * @param relativePath 相对路径（可选）
 * @param getFileInfo 获取文件信息的函数
 * @returns 配置好的 File 对象
 */
export async function createFileFromPath(filePath: string, relativePath?: string, getFileInfo?: (path: string) => Promise<any>): Promise<File> {
  if (!getFileInfo) {
    throw new Error("createFileFromPath 需要提供 getFileInfo 函数");
  }

  try {
    // 获取文件信息
    const response = await getFileInfo(filePath);

    // 检查两种可能的响应格式：直接返回文件信息 或 包装在 success/data 结构中
    let fileInfo;
    if (response?.success !== undefined) {
      // 包装格式：{success: true, data: {...}}
      if (!response.success || !response.data) {
        throw new Error(response?.error || "获取文件信息失败");
      }
      fileInfo = response.data;
    } else if (response && typeof response === "object" && (response as any).isFile !== undefined) {
      // 直接格式：{size: ..., mtime: ..., isFile: true, ...}
      fileInfo = response;
    } else {
      throw new Error("无效的文件信息响应格式");
    }

    // 提取文件名
    const fileName = extractFileName(filePath);

    // 创建虚拟的 File 对象，不实际读取文件内容
    // 在 Electron 环境中，我们主要使用文件路径进行上传
    const file = new File([], fileName, {
      type: "application/octet-stream",
      lastModified: fileInfo.mtime || Date.now(),
    });

    // 添加路径信息到 File 对象
    Object.defineProperty(file, "path", {
      value: filePath,
      writable: false,
    });

    Object.defineProperty(file, "size", {
      value: fileInfo.size || 0,
      writable: false,
    });

    // 只有当相对路径包含目录结构时才设置 webkitRelativePath
    if (hasPathStructure(relativePath, fileName)) {
      Object.defineProperty(file, "webkitRelativePath", {
        value: relativePath,
        writable: false,
      });
    }

    return file;
  } catch (error) {
    throw new Error(`无法创建文件对象: ${error}`);
  }
}

/**
 * 为前端拖拽创建空文件夹 File 对象（简化版本）
 *
 * @param folderName 文件夹名称
 * @param relativePath 相对路径
 * @returns 配置好的空文件夹 File 对象
 */
export function createEmptyFolderFileForDrop(folderName: string, relativePath: string): File {
  // 创建空文件夹的 File 对象
  const file = new File([], folderName, {
    type: "application/x-directory",
    lastModified: Date.now(),
  });

  // 设置相对路径
  Object.defineProperty(file, "webkitRelativePath", {
    value: relativePath,
    writable: false,
  });

  // 标记为空文件夹
  Object.defineProperty(file, "isFolder", {
    value: true,
    writable: false,
  });

  Object.defineProperty(file, "isEmpty", {
    value: true,
    writable: false,
  });

  return file;
}
