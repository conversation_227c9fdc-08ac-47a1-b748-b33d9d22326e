import { ref, computed, watch, nextTick } from "vue";
import { useTusUpload } from "@/components/Upload/composables/useTusUpload";
import { useStreamDownloadManager } from "@/composables/useStreamDownloadManager";
import { formatFileSize } from "@/lib/upload-utils";
import type { BatchUploadTask } from "@/types/electron";
import type { BatchDownloadTask } from "@/composables/useStreamDownloadManager";

/** 进度任务接口 */
export interface ProgressTask {
  id: string;
  type: "upload" | "download" | "archive";
  fileName: string;
  size?: string;
  progress: number;
  status: "pending" | "in-progress" | "paused" | "completed" | "error" | "cancelled";
  startTime: Date;
  endTime?: Date;
  error?: string;
  bytesUploaded?: number;
  totalBytes?: number;
  tusTaskId?: string; // 关联的 TUS 任务 ID
  downloadTaskId?: string; // 关联的下载任务 ID
  // 压缩任务特有字段
  archiveTaskId?: string; // 关联的压缩任务 ID
  currentFile?: string; // 当前正在处理的文件
  totalFiles?: number; // 总文件数量
  processedFiles?: number; // 已处理文件数量
}

/** 历史记录接口 */
export interface HistoryTask {
  id: string;
  type: "upload" | "download" | "archive";
  fileName: string;
  size?: string;
  startTime: Date;
  endTime: Date;
  duration: number; // 耗时(毫秒)
  status: "completed" | "error" | "cancelled";
  error?: string;
}

/** 批量历史记录接口 */
export interface BatchHistoryTask {
  id: string;
  type: "batch";
  batchType: "upload" | "download"; // 区分上传和下载批量任务
  batchName: string;
  folderPath?: string;
  totalFiles: number;
  completedFiles: number;
  failedFiles: number;
  totalSize: number;
  status: "completed" | "error" | "cancelled";
  startTime: Date;
  endTime: Date;
  duration: number;
  error?: string;
  subTasks: Array<{
    id: string;
    fileName: string;
    fileSize?: number;
    status: "completed" | "error" | "cancelled";
    error?: string;
  }>;
}

/**
 * 本地存储工具类
 * 负责管理任务历史记录的本地存储，支持上传和下载任务的分离存储
 */
class TaskHistoryStorage {
  private static readonly UPLOAD_HISTORY_KEY = "cloudDrive_uploadHistory";
  private static readonly DOWNLOAD_HISTORY_KEY = "cloudDrive_downloadHistory";
  private static readonly UPLOAD_BATCH_HISTORY_KEY = "cloudDrive_uploadBatchHistory";
  private static readonly DOWNLOAD_BATCH_HISTORY_KEY = "cloudDrive_downloadBatchHistory";

  /**
   * 从本地存储加载历史记录
   * @param key - 存储键名
   * @returns 历史记录数组
   */
  static loadHistory<T>(key: string): T[] {
    try {
      const stored = localStorage.getItem(key);
      if (stored) {
        const parsed = JSON.parse(stored);
        return parsed.map((item: any) => ({
          ...item,
          startTime: new Date(item.startTime),
          endTime: new Date(item.endTime),
        }));
      }
    } catch (error) {
      console.error(`Failed to load ${key} from localStorage:`, error);
    }
    return [];
  }

  /**
   * 保存历史记录到本地存储
   * @param key - 存储键名
   * @param data - 要保存的数据
   */
  static saveHistory<T>(key: string, data: T[]): void {
    try {
      localStorage.setItem(key, JSON.stringify(data));
      console.log(`历史记录已保存到 ${key}:`, data.length, "条记录");
    } catch (error) {
      console.error(`Failed to save ${key} to localStorage:`, error);
    }
  }

  /**
   * 立即保存历史记录，确保数据持久化
   * @param key - 存储键名
   * @param data - 要保存的数据
   * @returns Promise
   */
  static saveHistoryImmediate<T>(key: string, data: T[]): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        localStorage.setItem(key, JSON.stringify(data));
        console.log(`历史记录已立即保存到 ${key}:`, data.length, "条记录");
        resolve();
      } catch (error) {
        console.error(`Failed to immediately save ${key} to localStorage:`, error);
        reject(error);
      }
    });
  }

  /** 加载上传历史记录 */
  static loadUploadHistory(): HistoryTask[] {
    return this.loadHistory<HistoryTask>(this.UPLOAD_HISTORY_KEY);
  }

  /** 保存上传历史记录 */
  static saveUploadHistory(data: HistoryTask[]): void {
    this.saveHistory(this.UPLOAD_HISTORY_KEY, data);
  }

  /** 立即保存上传历史记录 */
  static saveUploadHistoryImmediate(data: HistoryTask[]): Promise<void> {
    return this.saveHistoryImmediate(this.UPLOAD_HISTORY_KEY, data);
  }

  /** 加载下载历史记录 */
  static loadDownloadHistory(): HistoryTask[] {
    return this.loadHistory<HistoryTask>(this.DOWNLOAD_HISTORY_KEY);
  }

  /** 保存下载历史记录 */
  static saveDownloadHistory(data: HistoryTask[]): void {
    this.saveHistory(this.DOWNLOAD_HISTORY_KEY, data);
  }

  /** 立即保存下载历史记录 */
  static saveDownloadHistoryImmediate(data: HistoryTask[]): Promise<void> {
    return this.saveHistoryImmediate(this.DOWNLOAD_HISTORY_KEY, data);
  }

  /** 加载上传批量任务历史记录 */
  static loadUploadBatchHistory(): BatchHistoryTask[] {
    return this.loadHistory<BatchHistoryTask>(this.UPLOAD_BATCH_HISTORY_KEY);
  }

  /** 保存上传批量任务历史记录 */
  static saveUploadBatchHistory(data: BatchHistoryTask[]): void {
    this.saveHistory(this.UPLOAD_BATCH_HISTORY_KEY, data);
  }

  /** 加载下载批量任务历史记录 */
  static loadDownloadBatchHistory(): BatchHistoryTask[] {
    return this.loadHistory<BatchHistoryTask>(this.DOWNLOAD_BATCH_HISTORY_KEY);
  }

  /** 保存下载批量任务历史记录 */
  static saveDownloadBatchHistory(data: BatchHistoryTask[]): void {
    this.saveHistory(this.DOWNLOAD_BATCH_HISTORY_KEY, data);
  }
}

/**
 * 全局进度管理 Composable
 * 统一管理上传和下载任务的进度状态，提供历史记录功能
 * @returns 全局进度管理的状态和方法
 */
export function useGlobalProgress() {
  const tusUpload = useTusUpload();
  const streamDownload = useStreamDownloadManager();

  // 任务状态
  const tasks = ref<ProgressTask[]>([]);

  // 历史记录状态 - 🔧 修复：从localStorage初始化历史记录
  const uploadHistoryTasks = ref<HistoryTask[]>(TaskHistoryStorage.loadUploadHistory());
  const downloadHistoryTasks = ref<HistoryTask[]>(TaskHistoryStorage.loadDownloadHistory());
  const uploadBatchHistoryTasks = ref<BatchHistoryTask[]>(TaskHistoryStorage.loadUploadBatchHistory());
  const downloadBatchHistoryTasks = ref<BatchHistoryTask[]>(TaskHistoryStorage.loadDownloadBatchHistory());

  // 🔧 初始化日志
  console.log(`📚 历史记录初始化完成:`);
  console.log(`  - 上传历史: ${uploadHistoryTasks.value.length} 条`);
  console.log(`  - 下载历史: ${downloadHistoryTasks.value.length} 条`);
  console.log(`  - 上传批量历史: ${uploadBatchHistoryTasks.value.length} 条`);
  console.log(`  - 下载批量历史: ${downloadBatchHistoryTasks.value.length} 条`);

  // 批量任务历史记录
  const batchHistoryTasks = computed(() => [...uploadBatchHistoryTasks.value, ...downloadBatchHistoryTasks.value]);

  // 向后兼容的历史记录
  const historyTasks = computed(() => [...uploadHistoryTasks.value, ...downloadHistoryTasks.value]);

  /**
   * 将TUS状态映射为进度状态
   * @param tusStatus - TUS任务状态
   * @returns 映射后的进度状态
   */
  const mapTusStatusToProgressStatus = (tusStatus: string): ProgressTask["status"] => {
    const statusMap: Record<string, ProgressTask["status"]> = {
      pending: "pending",
      uploading: "in-progress",
      paused: "paused",
      completed: "completed",
      error: "error",
      cancelled: "cancelled",
    };
    return statusMap[tusStatus] || "pending";
  };

  /**
   * 将下载状态映射为进度状态
   * 🔧 修复：处理解压缩相关状态，确保对用户透明
   * @param downloadStatus - 下载任务状态
   * @returns 映射后的进度状态
   */
  const mapDownloadStatusToProgressStatus = (downloadStatus: string): ProgressTask["status"] => {
    const statusMap: Record<string, ProgressTask["status"]> = {
      pending: "pending",
      downloading: "in-progress",
      paused: "paused",
      completed: "completed",
      error: "error",
      cancelled: "cancelled",

      // 🔒 解压缩相关状态映射 - 对用户透明
      "extract-completed": "completed", // 解压缩完成 -> 显示为完成
      extracting: "completed", // 正在解压缩 -> 显示为完成（解压缩过程对用户透明）
      "extract-error": "completed", // 解压缩失败 -> 显示为完成（下载本身是成功的）
    };

    const mappedStatus = statusMap[downloadStatus] || "pending";

    // 记录解压缩状态的映射（用于调试）
    if (downloadStatus.startsWith("extract")) {
      console.log(`🔄 进度状态映射: ${downloadStatus} -> ${mappedStatus} (对用户透明)`);
    }

    return mappedStatus;
  };

  // 计算属性 - 复用 TUS 的进度计算逻辑
  const activeTasks = computed(() => tasks.value.filter((task) => ["pending", "in-progress", "paused"].includes(task.status)));

  const uploadTasks = computed(() => tasks.value.filter((task) => task.type === "upload"));
  const downloadTasks = computed(() => tasks.value.filter((task) => task.type === "download"));
  const archiveTasks = computed(() => tasks.value.filter((task) => task.type === "archive"));
  const errorTasks = computed(() => tasks.value.filter((task) => task.status === "error"));

  // 复用 TUS 的总进度计算
  const overallProgress = computed(() => tusUpload.totalProgress.value);

  const hasActiveTasks = computed(() => {
    const progressTasks = tasks.value.filter((task) => ["pending", "in-progress", "paused"].includes(task.status));
    return progressTasks.length > 0 || tusUpload.activeTasks.value.length > 0 || streamDownload.activeTasks.value.length > 0;
  });

  // 最近完成的任务 - 直接从 TUS 获取，避免重复逻辑
  const recentCompletedTasks = computed(() => {
    return tusUpload.standaloneTasks.value
      .filter((task) => task.status === "completed")
      .map(
        (tusTask): ProgressTask => ({
          id: `progress-${tusTask.id}`,
          type: "upload",
          fileName: tusTask.fileName,
          size: formatFileSize(Number(tusTask.fileSize || 0)),
          progress: 100,
          status: "completed",
          startTime: new Date(tusTask.startTime),
          endTime: new Date(),
          tusTaskId: tusTask.id,
          bytesUploaded: Number(tusTask.fileSize || 0),
          totalBytes: Number(tusTask.fileSize || 0),
        })
      )
      .sort((a, b) => (b.endTime?.getTime() || 0) - (a.endTime?.getTime() || 0))
      .slice(0, 5);
  });

  // 最近完成的批量任务 - 直接从 TUS 获取
  const recentCompletedBatchTasks = computed(() => {
    return tusUpload.batchTasks.value
      .filter((batchTask: BatchUploadTask) => batchTask.status === "completed" && batchTask.endTime)
      .sort((a: BatchUploadTask, b: BatchUploadTask) => (b.endTime?.getTime() || 0) - (a.endTime?.getTime() || 0))
      .slice(0, 5);
  });

  // 历史记录计算属性
  const uploadHistory = computed(() => {
    // 只包含上传历史记录和上传批量任务
    const combined = [...uploadHistoryTasks.value, ...uploadBatchHistoryTasks.value];
    return combined.sort((a, b) => b.endTime.getTime() - a.endTime.getTime());
  });

  const downloadHistory = computed(() => {
    // 包含下载历史记录和下载批量任务
    const combined = [...downloadHistoryTasks.value, ...downloadBatchHistoryTasks.value];
    return combined.sort((a, b) => b.endTime.getTime() - a.endTime.getTime());
  });

  const allHistory = computed(() => {
    const combined = [...uploadHistoryTasks.value, ...downloadHistoryTasks.value, ...uploadBatchHistoryTasks.value, ...downloadBatchHistoryTasks.value];
    return combined.sort((a, b) => b.endTime.getTime() - a.endTime.getTime());
  });

  /**
   * 添加新任务到进度列表
   * @param taskInfo - 任务信息（不包含自动生成的字段）
   * @returns 生成的任务ID
   */
  const addTask = (taskInfo: Omit<ProgressTask, "id" | "progress" | "status" | "startTime">) => {
    const task: ProgressTask = {
      ...taskInfo,
      id: `${taskInfo.type}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      progress: 0,
      status: "pending",
      startTime: new Date(),
    };
    tasks.value.push(task);
    return task.id;
  };

  /**
   * 更新任务进度
   * @param taskId - 任务ID
   * @param progress - 进度百分比（0-100）
   */
  const updateTaskProgress = (taskId: string, progress: number) => {
    const task = tasks.value.find((t) => t.id === taskId);
    if (task) {
      task.progress = Math.max(0, Math.min(100, progress));
      task.status = progress >= 100 ? "completed" : "in-progress";
    }
  };

  /**
   * 将任务添加到历史记录
   * 根据任务类型分别保存到对应的历史记录中
   * @param task - 要添加到历史记录的任务或历史任务
   */
  const addToHistory = async (task: ProgressTask | HistoryTask) => {
    let historyItem: HistoryTask;

    if ("duration" in task) {
      // 已经是 HistoryTask 类型
      historyItem = task;
    } else if (task.endTime) {
      // 从 ProgressTask 转换为 HistoryTask
      historyItem = {
        id: task.id,
        type: task.type,
        fileName: task.fileName,
        size: task.size,
        startTime: task.startTime,
        endTime: task.endTime,
        duration: task.endTime.getTime() - task.startTime.getTime(),
        status: task.status as "completed" | "error" | "cancelled",
        error: task.error,
      };
    } else {
      return; // 没有结束时间，不添加到历史记录
    }

    // 根据任务类型添加到对应的历史记录
    if (historyItem.type === "upload") {
      // 🔒 去重检查：检查任务是否已经在上传历史记录中
      const existingUploadHistory = uploadHistoryTasks.value.find((h) => h.id === historyItem.id);
      if (existingUploadHistory) {
        console.log(`⚠️ 任务已存在于上传历史记录中，跳过添加: ${historyItem.fileName} (ID: ${historyItem.id})`);
        return;
      }

      console.log(`添加任务到上传历史记录: ${historyItem.fileName} (${historyItem.type})`);
      uploadHistoryTasks.value.unshift(historyItem);

      // 限制上传历史记录数量
      if (uploadHistoryTasks.value.length > 1000) {
        uploadHistoryTasks.value = uploadHistoryTasks.value.slice(0, 1000);
      }

      // 立即保存上传历史记录
      try {
        await TaskHistoryStorage.saveUploadHistoryImmediate(uploadHistoryTasks.value);
      } catch (error) {
        console.error("保存上传历史记录失败:", error);
      }
    } else if (task.type === "download") {
      // 🔒 去重检查：检查任务是否已经在下载历史记录中
      const existingDownloadHistory = downloadHistoryTasks.value.find((h) => h.id === historyItem.id);
      if (existingDownloadHistory) {
        console.log(`⚠️ 任务已存在于下载历史记录中，跳过添加: ${historyItem.fileName} (ID: ${historyItem.id})`);
        return;
      }

      console.log(`添加任务到下载历史记录: ${historyItem.fileName} (${historyItem.type})`);
      downloadHistoryTasks.value.unshift(historyItem);

      // 限制下载历史记录数量
      if (downloadHistoryTasks.value.length > 1000) {
        downloadHistoryTasks.value = downloadHistoryTasks.value.slice(0, 1000);
      }

      // 立即保存下载历史记录
      try {
        await TaskHistoryStorage.saveDownloadHistoryImmediate(downloadHistoryTasks.value);
        console.log(`下载历史记录已保存: ${historyItem.fileName}`);
      } catch (error) {
        console.error("保存下载历史记录失败:", error);
      }
    }
  };

  /**
   * 完成任务
   * 将任务标记为完成状态并添加到历史记录
   * @param taskId - 任务ID
   */
  const completeTask = (taskId: string) => {
    const task = tasks.value.find((t) => t.id === taskId);
    if (task) {
      task.status = "completed";
      task.progress = 100;
      task.endTime = new Date();
      addToHistory(task);
      removeTask(taskId);
    }
  };

  /**
   * 标记任务为错误状态
   * 将任务标记为错误状态并添加到历史记录
   * @param taskId - 任务ID
   * @param error - 错误信息
   */
  const errorTask = (taskId: string, error: string) => {
    const task = tasks.value.find((t) => t.id === taskId);
    if (task) {
      task.status = "error";
      task.error = error;
      task.endTime = new Date();
      addToHistory(task);
      removeTask(taskId);
    }
  };

  /**
   * 从当前任务列表中移除任务
   * @param taskId - 任务ID
   */
  const removeTask = (taskId: string) => {
    const index = tasks.value.findIndex((t) => t.id === taskId);
    if (index > -1) {
      tasks.value.splice(index, 1);
    }
  };

  /**
   * 获取任务
   * @param taskId - 任务ID
   */
  const getTask = (taskId: string) => {
    return tasks.value.find((t) => t.id === taskId);
  };

  // 真正删除任务（包括 Electron 端）
  const deleteTask = async (taskId: string) => {
    const task = getTask(taskId);
    if (!task) return false;

    try {
      let success = false;

      if (task.tusTaskId) {
        // 删除上传任务
        success = await tusUpload.deleteTask(task.tusTaskId);
      } else if (task.downloadTaskId) {
        // 删除下载任务
        await streamDownload.removeTask(task.downloadTaskId);
        success = true;
      }

      if (success) {
        // 从前端进度列表中移除
        removeTask(taskId);
      }

      return success;
    } catch (error) {
      console.error("删除任务失败:", error);
      return false;
    }
  };

  const clearCompletedTasks = () => {
    // 已完成的任务已自动移动到历史记录，当前任务列表中不会有已完成的任务
    // 这个方法保留用于兼容性，实际上不需要做任何操作
  };

  const clearAllTasks = async () => {
    // 清空前端当前任务
    tasks.value = [];

    // 同步清空 Electron 端的所有任务
    try {
      // 清空下载任务
      await streamDownload.clearAllTasks();
      console.log("✅ 已清空 Electron 端下载任务");
    } catch (error) {
      console.error("❌ 清空 Electron 端下载任务失败:", error);
    }

    // 清空上传任务
    try {
      await tusUpload.clearAllTasks();
      console.log("✅ 已清空 Electron 端上传任务");
    } catch (error) {
      console.error("❌ 清空 Electron 端上传任务失败:", error);
    }
  };

  /**
   * 清空所有历史记录
   * 清空前端和Electron端的所有历史记录
   * 🔧 修复：同步保存到localStorage
   */
  const clearHistory = async () => {
    console.log("清空所有历史记录");

    // 清空前端历史记录
    uploadHistoryTasks.value = [];
    downloadHistoryTasks.value = [];
    uploadBatchHistoryTasks.value = [];
    downloadBatchHistoryTasks.value = [];

    // 🔧 修复：立即保存到localStorage
    try {
      await Promise.all([
        TaskHistoryStorage.saveUploadHistoryImmediate(uploadHistoryTasks.value),
        TaskHistoryStorage.saveDownloadHistoryImmediate(downloadHistoryTasks.value),
        TaskHistoryStorage.saveUploadBatchHistory(uploadBatchHistoryTasks.value),
        TaskHistoryStorage.saveDownloadBatchHistory(downloadBatchHistoryTasks.value),
      ]);
      console.log("✅ 所有历史记录已清空并保存到localStorage");
    } catch (error) {
      console.error("❌ 保存清空的历史记录到localStorage失败:", error);
    }

    // 同步清空 Electron 端的已完成任务
    try {
      await streamDownload.clearCompletedTasks();
      console.log("已清空 Electron 端下载历史");
    } catch (error) {
      console.error("清空 Electron 端下载历史失败:", error);
    }

    // 同步清空 Electron 端的已完成上传任务
    try {
      await tusUpload.clearCompletedTasks();
      console.log("已清空 Electron 端上传历史");
    } catch (error) {
      console.error("清空 Electron 端上传历史失败:", error);
    }
  };

  /**
   * 清空上传历史记录
   * 清空所有上传相关的历史记录
   * 🔧 修复：同步保存到localStorage
   */
  const clearUploadHistory = async () => {
    console.log("清空上传历史记录");

    // 清空上传历史记录
    uploadHistoryTasks.value = [];

    // 清空上传批量任务历史记录
    uploadBatchHistoryTasks.value = [];

    // 🔧 修复：立即保存到localStorage
    try {
      await Promise.all([TaskHistoryStorage.saveUploadHistoryImmediate(uploadHistoryTasks.value), TaskHistoryStorage.saveUploadBatchHistory(uploadBatchHistoryTasks.value)]);
      console.log("✅ 上传历史记录已清空并保存到localStorage");
    } catch (error) {
      console.error("❌ 保存清空的上传历史记录到localStorage失败:", error);
    }

    // 同步清空 Electron 端的已完成上传任务
    try {
      await tusUpload.clearCompletedTasks();
      console.log("Electron 端上传历史已清空");
    } catch (error) {
      console.error("清空 Electron 端上传历史失败:", error);
    }

    console.log("上传历史记录已清空");
  };

  /**
   * 清空下载历史记录
   * 清空前端和Electron端的下载历史记录
   */
  const clearDownloadHistory = async () => {
    console.log("清空下载历史记录");

    // 清空前端下载历史记录
    downloadHistoryTasks.value = [];

    // 清空下载批量任务历史记录
    downloadBatchHistoryTasks.value = [];

    // 🔧 修复：立即保存所有下载历史记录
    try {
      await Promise.all([TaskHistoryStorage.saveDownloadHistoryImmediate(downloadHistoryTasks.value), TaskHistoryStorage.saveDownloadBatchHistory(downloadBatchHistoryTasks.value)]);
      console.log("✅ 下载历史记录已清空并保存到localStorage");
    } catch (error) {
      console.error("❌ 保存清空的下载历史记录到localStorage失败:", error);
    }

    // 同步清空 Electron 端的已完成下载任务
    try {
      await streamDownload.clearCompletedTasks();
      console.log("Electron 端下载历史已清空");
    } catch (error) {
      console.error("清空 Electron 端下载历史失败:", error);
    }
  };

  /**
   * 删除指定的历史记录项
   * 从所有历史记录类型中查找并删除指定ID的记录
   * 🔧 修复：删除后立即保存到localStorage
   * @param historyId - 历史记录ID
   */
  const removeHistoryItem = async (historyId: string) => {
    console.log(`删除历史记录项: ${historyId}`);

    // 从上传历史记录中查找并删除
    const uploadIndex = uploadHistoryTasks.value.findIndex((item) => item.id === historyId);
    if (uploadIndex > -1) {
      uploadHistoryTasks.value.splice(uploadIndex, 1);
      console.log(`已从上传历史记录中删除: ${historyId}`);
      // 🔧 立即保存到localStorage
      try {
        await TaskHistoryStorage.saveUploadHistoryImmediate(uploadHistoryTasks.value);
      } catch (error) {
        console.error("保存上传历史记录失败:", error);
      }
      return;
    }

    // 从下载历史记录中查找并删除
    const downloadIndex = downloadHistoryTasks.value.findIndex((item) => item.id === historyId);
    if (downloadIndex > -1) {
      downloadHistoryTasks.value.splice(downloadIndex, 1);
      console.log(`已从下载历史记录中删除: ${historyId}`);
      // 🔧 立即保存到localStorage
      try {
        await TaskHistoryStorage.saveDownloadHistoryImmediate(downloadHistoryTasks.value);
      } catch (error) {
        console.error("保存下载历史记录失败:", error);
      }
      return;
    }

    // 从上传批量任务历史记录中查找并删除
    const uploadBatchIndex = uploadBatchHistoryTasks.value.findIndex((item) => item.id === historyId);
    if (uploadBatchIndex > -1) {
      uploadBatchHistoryTasks.value.splice(uploadBatchIndex, 1);
      console.log(`已从上传批量任务历史记录中删除: ${historyId}`);
      // 🔧 立即保存到localStorage
      try {
        TaskHistoryStorage.saveUploadBatchHistory(uploadBatchHistoryTasks.value);
      } catch (error) {
        console.error("保存上传批量历史记录失败:", error);
      }
      return;
    }

    // 从下载批量任务历史记录中查找并删除
    const downloadBatchIndex = downloadBatchHistoryTasks.value.findIndex((item) => item.id === historyId);
    if (downloadBatchIndex > -1) {
      downloadBatchHistoryTasks.value.splice(downloadBatchIndex, 1);
      console.log(`已从下载批量任务历史记录中删除: ${historyId}`);
      // 🔧 立即保存到localStorage
      try {
        TaskHistoryStorage.saveDownloadBatchHistory(downloadBatchHistoryTasks.value);
      } catch (error) {
        console.error("保存下载批量历史记录失败:", error);
      }
      return;
    }

    console.warn(`未找到要删除的历史记录项: ${historyId}`);
  };

  /**
   * 将上传批量任务添加到历史记录
   * @param batchTask - 上传批量任务
   */
  const addBatchToHistory = (batchTask: BatchUploadTask) => {
    if (batchTask.status === "completed" && batchTask.endTime) {
      const batchHistoryItem: BatchHistoryTask = {
        id: batchTask.id,
        type: "batch",
        batchType: "upload", // 标识为上传批量任务
        batchName: batchTask.batchName,
        folderPath: batchTask.folderPath,
        totalFiles: batchTask.totalFiles,
        completedFiles: batchTask.completedFiles,
        failedFiles: batchTask.failedFiles,
        totalSize: batchTask.totalSize,
        status: batchTask.status,
        startTime: batchTask.startTime,
        endTime: batchTask.endTime,
        duration: batchTask.endTime.getTime() - batchTask.startTime.getTime(),
        subTasks: batchTask.subTasks.map((taskId) => {
          const subTask = tusUpload.tasks.value.find((t) => t.id === taskId);
          return {
            id: taskId,
            fileName: subTask?.fileName || "未知文件",
            fileSize: Number(subTask?.fileSize || 0),
            status: subTask?.status === "completed" ? "completed" : subTask?.status === "error" ? "error" : "cancelled",
            error: subTask?.error,
          };
        }),
      };

      uploadBatchHistoryTasks.value.unshift(batchHistoryItem);
      console.log(`添加上传批量任务到历史记录: ${batchTask.batchName}`);

      // 限制批量历史记录数量
      if (uploadBatchHistoryTasks.value.length > 500) {
        uploadBatchHistoryTasks.value = uploadBatchHistoryTasks.value.slice(0, 500);
      }
    }
  };

  /**
   * 将下载批量任务添加到历史记录
   * @param batchTask - 下载批量任务
   */
  const addDownloadBatchToHistory = (batchTask: BatchDownloadTask) => {
    if (batchTask.status === "completed" && batchTask.endTime) {
      // 将文件夹下载任务作为批量历史任务添加到 batchHistoryTasks
      const batchHistoryItem: BatchHistoryTask = {
        id: batchTask.id,
        type: "batch",
        batchType: "download", // 标识为下载批量任务
        batchName: batchTask.batchName,
        folderPath: batchTask.folderPath,
        totalFiles: batchTask.totalFiles,
        completedFiles: batchTask.completedFiles,
        failedFiles: batchTask.failedFiles,
        totalSize: batchTask.totalSize,
        status: batchTask.status,
        startTime: batchTask.startTime,
        endTime: batchTask.endTime,
        duration: batchTask.endTime.getTime() - batchTask.startTime.getTime(),
        subTasks: batchTask.subTasks.map((taskId) => {
          const subTask = streamDownload.tasksMap.value.get(taskId);
          return {
            id: taskId,
            fileName: subTask?.fileName || "未知文件",
            fileSize: subTask?.fileSize || 0,
            status: subTask?.status === "completed" ? "completed" : subTask?.status === "error" ? "error" : "cancelled",
            error: subTask?.error,
          };
        }),
      };

      downloadBatchHistoryTasks.value.unshift(batchHistoryItem);
      console.log(`文件夹下载任务已添加到批量历史记录: ${batchTask.batchName} (${batchTask.completedFiles}/${batchTask.totalFiles} 个文件完成)`);

      // 限制批量历史记录数量
      if (downloadBatchHistoryTasks.value.length > 500) {
        downloadBatchHistoryTasks.value = downloadBatchHistoryTasks.value.slice(0, 500);
      }

      // 立即保存批量历史记录
      try {
        TaskHistoryStorage.saveDownloadBatchHistory(downloadBatchHistoryTasks.value);
        console.log(`文件夹下载批量历史记录已保存: ${batchTask.batchName}`);
      } catch (error) {
        console.error("保存文件夹下载批量历史记录失败:", error);
      }
    }
  };

  /**
   * 从TUS同步任务状态到进度列表
   * 同步独立上传任务的状态和进度信息
   */
  const syncTusTasksToProgress = () => {
    // 同步独立任务（非子任务）
    tusUpload.standaloneTasks.value.forEach((tusTask) => {
      const existingTask = tasks.value.find((t) => t.tusTaskId === tusTask.id);

      // 🔧 修复：只同步未完成的任务，排除已完成和失败状态
      if (!existingTask && !["completed", "error", "cancelled"].includes(tusTask.status)) {
        const progressTask: ProgressTask = {
          id: `progress-${tusTask.id}`,
          type: "upload",
          fileName: tusTask.fileName,
          size: formatFileSize(Number(tusTask.fileSize || 0)),
          progress: tusTask.progress,
          status: mapTusStatusToProgressStatus(tusTask.status),
          startTime: new Date(tusTask.startTime),
          tusTaskId: tusTask.id,
          bytesUploaded: tusTask.bytesUploaded,
          totalBytes: Number(tusTask.fileSize || 0),
        };
        tasks.value.push(progressTask);
      } else if (existingTask) {
        // 更新现有任务状态
        existingTask.progress = tusTask.progress;
        existingTask.status = mapTusStatusToProgressStatus(tusTask.status);
        existingTask.bytesUploaded = tusTask.bytesUploaded;

        // 🔧 修复：处理任务完成、错误或取消的情况，包括失败任务自动移动到历史记录
        if (["completed", "error", "cancelled"].includes(tusTask.status) && !existingTask.endTime) {
          console.log(`📤 上传任务状态变更: ${existingTask.fileName} -> ${tusTask.status} (ID: ${existingTask.id})`);

          // 🔒 双重检查：确保任务还没有被添加到历史记录中
          const alreadyInHistory = uploadHistoryTasks.value.find((h) => h.id === existingTask.id);
          if (alreadyInHistory) {
            console.log(`⚠️ 任务已在历史记录中，跳过处理: ${existingTask.fileName} (ID: ${existingTask.id})`);
            return;
          }

          existingTask.endTime = new Date();
          if (tusTask.error) {
            existingTask.error = tusTask.error;
          }

          // 使用 nextTick 确保状态更新完成后再处理历史记录
          nextTick(async () => {
            try {
              // 🔒 再次检查任务是否已经在历史记录中（防止并发问题）
              const stillNotInHistory = !uploadHistoryTasks.value.find((h) => h.id === existingTask.id);
              if (!stillNotInHistory) {
                console.log(`⚠️ 任务在处理过程中已被添加到历史记录，跳过: ${existingTask.fileName} (ID: ${existingTask.id})`);
                return;
              }

              // 🔧 新增：失败任务自动移动到历史记录
              await addToHistory(existingTask);
              console.log(`✅ 上传任务已添加到历史记录: ${existingTask.fileName} (状态: ${tusTask.status}) (ID: ${existingTask.id})`);

              // 在历史记录保存完成后再从当前任务列表中移除
              removeTask(existingTask.id);
              console.log(`🗑️ 上传任务已从当前列表移除: ${existingTask.fileName} (ID: ${existingTask.id})`);
            } catch (error) {
              console.error(`❌ 处理上传任务状态失败: ${existingTask.fileName} (ID: ${existingTask.id})`, error);

              // 🔧 新增：即使添加历史记录失败，也要从当前列表中移除失败任务，避免任务卡在列表中
              if (["error", "cancelled"].includes(tusTask.status)) {
                removeTask(existingTask.id);
                console.log(`🗑️ 强制从当前列表移除失败的上传任务: ${existingTask.fileName} (ID: ${existingTask.id})`);
              }
            }
          });
        }
      }
    });
  };

  /**
   * 从下载管理器同步任务状态到进度列表
   * 同步独立下载任务的状态和进度信息
   * 🔧 修复：过滤已完成任务，包括解压缩相关状态
   */
  const syncDownloadTasksToProgress = () => {
    // 只同步独立下载任务（排除文件夹下载的子任务）

    streamDownload.standaloneTasks.value.forEach((downloadTask) => {
      const existingTask = tasks.value.find((t) => t.downloadTaskId === downloadTask.id);

      // 🔒 定义已完成状态（包括解压缩相关状态和失败状态）
      const completedStatuses = ["completed", "extract-completed", "extracting", "extract-error", "cancelled", "error"];

      // 🔧 修复：过滤已完成任务，不添加到当前任务列表
      if (!existingTask && !completedStatuses.includes(downloadTask.status)) {
        const formattedSize = downloadTask.fileSize ? formatFileSize(downloadTask.fileSize) : undefined;

        const progressTask: ProgressTask = {
          id: `progress-${downloadTask.id}`,
          type: "download",
          fileName: downloadTask.fileName,
          size: formattedSize,
          progress: downloadTask.progress,
          status: mapDownloadStatusToProgressStatus(downloadTask.status),
          startTime: downloadTask.startTime,
          downloadTaskId: downloadTask.id,
          bytesUploaded: downloadTask.bytesDownloaded,
          totalBytes: downloadTask.fileSize || 0,
        };
        tasks.value.push(progressTask);
        console.log(`📥 添加下载任务到进度列表: ${progressTask.fileName} (状态: ${downloadTask.status})`);
      } else if (existingTask) {
        // 更新现有任务状态
        existingTask.progress = downloadTask.progress;
        existingTask.status = mapDownloadStatusToProgressStatus(downloadTask.status);
        existingTask.bytesUploaded = downloadTask.bytesDownloaded;
        existingTask.totalBytes = downloadTask.fileSize || 0;

        // 更新文件大小显示（如果文件大小发生变化）
        if (downloadTask.fileSize && downloadTask.fileSize > 0) {
          const newFormattedSize = formatFileSize(downloadTask.fileSize);
          if (existingTask.size !== newFormattedSize) {
            existingTask.size = newFormattedSize;
          }
        }

        // 🔧 修复：处理任务完成、错误或取消的情况，包括失败任务自动移动到历史记录
        if (["completed", "extract-completed", "error", "cancelled"].includes(downloadTask.status) && !existingTask.endTime) {
          console.log(`📋 下载任务状态变更: ${existingTask.fileName} -> ${downloadTask.status} (ID: ${existingTask.id})`);

          // 🔒 双重检查：确保任务还没有被添加到历史记录中
          const alreadyInHistory = downloadHistoryTasks.value.find((h) => h.id === existingTask.id);
          if (alreadyInHistory) {
            console.log(`⚠️ 任务已在历史记录中，跳过处理: ${existingTask.fileName} (ID: ${existingTask.id})`);
            return;
          }

          existingTask.endTime = new Date();
          if (downloadTask.error) {
            existingTask.error = downloadTask.error;
          }

          // 使用 nextTick 确保状态更新完成后再处理历史记录
          nextTick(async () => {
            try {
              // 🔒 再次检查任务是否已经在历史记录中（防止并发问题）
              const stillNotInHistory = !downloadHistoryTasks.value.find((h) => h.id === existingTask.id);
              if (!stillNotInHistory) {
                console.log(`⚠️ 任务在处理过程中已被添加到历史记录，跳过: ${existingTask.fileName} (ID: ${existingTask.id})`);
                return;
              }

              // 创建历史记录
              const historyTask: HistoryTask = {
                id: existingTask.id,
                type: existingTask.type,
                fileName: existingTask.fileName,
                size: existingTask.size,
                startTime: existingTask.startTime,
                endTime: existingTask.endTime!,
                duration: existingTask.endTime!.getTime() - existingTask.startTime.getTime(),
                status: existingTask.status as "completed" | "error" | "cancelled",
                error: existingTask.error,
              };

              // 🔧 新增：失败任务自动移动到历史记录
              await addToHistory(historyTask);
              console.log(`✅ 下载任务已添加到历史记录: ${existingTask.fileName} (状态: ${downloadTask.status}) (ID: ${existingTask.id})`);

              // 在历史记录保存完成后再从当前任务列表中移除
              removeTask(existingTask.id);
              console.log(`🗑️ 下载任务已从当前列表移除: ${existingTask.fileName} (ID: ${existingTask.id})`);
            } catch (error) {
              console.error(`❌ 处理下载任务状态失败: ${existingTask.fileName} (ID: ${existingTask.id})`, error);

              // 🔧 新增：即使添加历史记录失败，也要从当前列表中移除失败任务，避免任务卡在列表中
              if (["error", "cancelled"].includes(downloadTask.status)) {
                removeTask(existingTask.id);
                console.log(`🗑️ 强制从当前列表移除失败的下载任务: ${existingTask.fileName} (ID: ${existingTask.id})`);
              }
            }
          });
        }
      }
    });
  };

  // 自动同步批量任务到历史记录
  const syncBatchTasksToHistory = () => {
    tusUpload.batchTasks.value.forEach((batchTask: BatchUploadTask) => {
      if (batchTask.status === "completed" && batchTask.endTime) {
        const existingHistory = batchHistoryTasks.value.find((h) => h.id === batchTask.id);
        if (!existingHistory) {
          addBatchToHistory(batchTask);
        }
      }
    });
  };

  /**
   * 自动同步下载批量任务到历史记录
   * 检查已完成的下载批量任务并添加到历史记录
   */
  const syncDownloadBatchTasksToHistory = () => {
    const batchTasksArray = Array.from(streamDownload.batchTasks.value.values());

    batchTasksArray.forEach((batchTask: BatchDownloadTask) => {
      if (batchTask.status === "completed" && batchTask.endTime) {
        // 检查是否已经在下载批量历史记录中
        const existingHistory = downloadBatchHistoryTasks.value.find((h) => h.id === batchTask.id);
        if (!existingHistory) {
          addDownloadBatchToHistory(batchTask);
        }
      }
    });
  };

  /**
   * 立即同步所有任务状态
   * 同步上传、下载任务的状态和批量任务的历史记录
   */
  const syncAllTasks = () => {
    syncTusTasksToProgress();
    syncBatchTasksToHistory();
    syncDownloadTasksToProgress();
    syncDownloadBatchTasksToHistory();
  };

  // 监听TUS任务变化，立即同步
  watch(
    () => tusUpload.standaloneTasks.value,
    () => {
      syncTusTasksToProgress();
    },
    { deep: true, immediate: true }
  );

  watch(
    () => tusUpload.batchTasks.value,
    () => {
      syncBatchTasksToHistory();
    },
    { deep: true, immediate: true }
  );

  // 监听下载任务变化，立即同步（只监听独立任务，排除文件夹下载的子任务）
  watch(
    () => streamDownload.standaloneTasks.value,
    () => {
      syncDownloadTasksToProgress();
    },
    { deep: true, immediate: true }
  );

  watch(
    () => streamDownload.batchTasks.value,
    () => {
      syncDownloadBatchTasksToHistory();
    },
    { deep: true, immediate: true }
  );

  /**
   * 创建压缩任务
   * @param archiveTaskId - 压缩任务ID
   * @param fileName - 压缩包文件名
   * @param totalFiles - 总文件数量
   * @param metadata - 任务元数据
   * @returns 生成的进度任务ID
   */
  const addArchiveTask = (archiveTaskId: string, fileName: string, totalFiles: number, metadata?: Record<string, any>) => {
    // 检查是否已存在相同的任务
    const existingTask = tasks.value.find((t) => t.archiveTaskId === archiveTaskId);
    if (existingTask) {
      console.log(`📦 压缩任务已存在，跳过创建: ${archiveTaskId}`);
      return existingTask.id;
    }

    const task: ProgressTask = {
      id: `archive-${archiveTaskId}`,
      type: "archive",
      fileName,
      progress: 0,
      status: "in-progress", // 立即设置为进行中状态
      startTime: new Date(),
      archiveTaskId,
      totalFiles,
      processedFiles: 0,
      currentFile: undefined,
    };
    tasks.value.push(task);
    console.log(`📦 创建压缩进度任务: ${fileName} (${totalFiles} 个文件)，任务ID: ${task.id}`);
    return task.id;
  };

  /**
   * 更新压缩任务进度
   * @param archiveTaskId - 压缩任务ID
   * @param progress - 进度百分比（0-100）
   * @param currentFile - 当前处理的文件
   */
  const updateArchiveTaskProgress = (archiveTaskId: string, progress: number, currentFile?: string) => {
    const task = tasks.value.find((t) => t.archiveTaskId === archiveTaskId);
    if (task) {
      const oldProgress = task.progress;
      task.progress = Math.max(0, Math.min(100, progress));
      task.status = progress >= 100 ? "completed" : "in-progress";
      if (currentFile) {
        task.currentFile = currentFile;
      }
      // 根据进度估算已处理文件数
      if (task.totalFiles) {
        task.processedFiles = Math.floor((progress / 100) * task.totalFiles);
      }

      // 只在进度有显著变化时记录日志
      if (Math.abs(progress - oldProgress) >= 1 || currentFile) {
        console.log(`📦 更新压缩进度: ${task.fileName} -> ${progress}% (${task.processedFiles}/${task.totalFiles}) - ${currentFile || ""}`);
      }
    } else {
      console.warn(`📦 未找到压缩任务: ${archiveTaskId}`);
    }
  };

  /**
   * 完成压缩任务
   * @param archiveTaskId - 压缩任务ID
   * @param archivePath - 压缩包路径
   */
  const completeArchiveTask = (archiveTaskId: string, archivePath?: string) => {
    const task = tasks.value.find((t) => t.archiveTaskId === archiveTaskId);
    if (task) {
      task.status = "completed";
      task.progress = 100;
      task.endTime = new Date();
      if (task.totalFiles) {
        task.processedFiles = task.totalFiles;
      }
      console.log(`📦 压缩任务完成: ${task.fileName}`);

      // 添加到历史记录
      addToHistory(task);

      // 从当前任务列表中移除
      removeTask(task.id);
    }
  };

  /**
   * 压缩任务出错
   * @param archiveTaskId - 压缩任务ID
   * @param error - 错误信息
   */
  const errorArchiveTask = (archiveTaskId: string, error: string) => {
    const task = tasks.value.find((t) => t.archiveTaskId === archiveTaskId);
    if (task) {
      task.status = "error";
      task.error = error;
      task.endTime = new Date();
      console.error(`📦 压缩任务出错: ${task.fileName} - ${error}`);

      // 添加到历史记录
      addToHistory(task);

      // 从当前任务列表中移除
      removeTask(task.id);
    }
  };

  /**
   * 清理方法
   * 清理组件卸载时需要释放的资源
   */
  const cleanup = () => {
    // 如果有其他需要清理的资源，在这里添加
  };

  return {
    // 状态
    tasks,
    activeTasks,
    uploadTasks,
    downloadTasks,
    archiveTasks,
    errorTasks,
    overallProgress,
    hasActiveTasks,
    recentCompletedTasks,
    recentCompletedBatchTasks,

    // 历史记录（向后兼容）
    historyTasks,
    batchHistoryTasks,
    uploadHistory,
    downloadHistory,
    allHistory,

    // 分离的历史记录状态
    uploadHistoryTasks,
    downloadHistoryTasks,

    // 任务操作方法
    addTask,
    updateTaskProgress,
    completeTask,
    errorTask,
    removeTask,
    deleteTask,
    clearCompletedTasks,
    clearAllTasks,
    getTask,

    // 压缩任务操作方法
    addArchiveTask,
    updateArchiveTaskProgress,
    completeArchiveTask,
    errorArchiveTask,

    // 历史记录操作方法
    addToHistory,
    clearHistory,
    clearUploadHistory,
    clearDownloadHistory,
    removeHistoryItem,
    addBatchToHistory,

    // 同步方法
    syncTusTasksToProgress,
    syncBatchTasksToHistory,
    syncDownloadTasksToProgress,
    syncDownloadBatchTasksToHistory,
    syncAllTasks,

    // 清理方法
    cleanup,

    // 兼容性方法 (保持 API 兼容)
    cancelTask: async (taskId: string) => {
      const task = getTask(taskId);
      if (task?.tusTaskId) {
        return await tusUpload.cancelUpload(task.tusTaskId);
      } else if (task?.downloadTaskId) {
        return await streamDownload.cancelDownload(task.downloadTaskId);
      } else if (task?.archiveTaskId) {
        // 处理压缩任务取消
        try {
          const api = (window as any).electronAPI;
          if (api && api.archive && api.archive.cancelTask) {
            const response = await api.archive.cancelTask(task.archiveTaskId);
            return response.success;
          }
        } catch (error) {
          console.error("取消压缩任务失败:", error);
        }
        return false;
      }
      return false;
    },
    retryTask: async (taskId: string) => {
      const task = getTask(taskId);
      if (!task) {
        console.warn(`⚠️ 重试失败：未找到任务 ${taskId}`);
        return false;
      }

      console.log(`🔄 开始重试任务: ${task.fileName} (ID: ${taskId})`);

      try {
        let retrySuccess = false;

        if (task.tusTaskId) {
          retrySuccess = await tusUpload.retryUpload(task.tusTaskId);
        } else if (task.downloadTaskId) {
          retrySuccess = await streamDownload.retryDownload(task.downloadTaskId);
        }

        console.log(`🔄 重试API调用结果: ${task.fileName} -> ${retrySuccess ? "成功启动" : "启动失败"}`);

        // 🔧 简化：重试后的成功/失败状态将通过现有的同步机制自动处理
        // 不需要额外的监听和失败处理逻辑
        if (retrySuccess) {
          console.log(`✅ 重试任务已启动: ${task.fileName} (ID: ${taskId})，后续状态变化将通过同步机制处理`);
        } else {
          console.log(`❌ 重试任务启动失败: ${task.fileName} (ID: ${taskId})，任务状态将通过同步机制处理`);
        }

        return retrySuccess;
      } catch (error) {
        console.error(`❌ 重试任务异常: ${task.fileName} (ID: ${taskId})`, error);
        return false;
      }
    },
    pauseTask: async (taskId: string) => {
      const task = getTask(taskId);
      if (task?.tusTaskId) {
        return await tusUpload.pauseUpload(task.tusTaskId);
      } else if (task?.downloadTaskId) {
        return await streamDownload.pauseDownload(task.downloadTaskId);
      }
      return false;
    },
    resumeTask: async (taskId: string) => {
      const task = getTask(taskId);
      if (task?.tusTaskId) {
        return await tusUpload.resumeUpload(task.tusTaskId);
      } else if (task?.downloadTaskId) {
        return await streamDownload.resumeDownload(task.downloadTaskId);
      }
      return false;
    },

    // 工具方法 (复用已有的)
    formatFileSize,
  };
}
