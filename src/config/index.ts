import type { AppConfig } from "@/types/env";

/**
 * 获取环境变量值，如果不存在则返回默认值
 */
function getEnvValue(key: keyof ImportMetaEnv, defaultValue: string = ""): string {
  return import.meta.env[key] || defaultValue;
}

/**
 * 将字符串转换为数字
 */
function toNumber(value: string, defaultValue: number = 0): number {
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? defaultValue : parsed;
}

/**
 * 将字符串转换为布尔值
 */
function toBoolean(value: string, defaultValue: boolean = false): boolean {
  if (!value) return defaultValue;
  return value.toLowerCase() === "true";
}

/**
 * 应用配置
 */
export const config: AppConfig = {
  app: {
    title: getEnvValue("VITE_APP_TITLE", "CloudDrive"),
    version: getEnvValue("VITE_APP_VERSION", "1.0.0"),
  },
  api: {
    baseURL: getEnvValue("VITE_API_BASE_URL", "/api"),
    timeout: toNumber(getEnvValue("VITE_API_TIMEOUT"), 10000),
  },
  tus: {
    endpoint: getEnvValue("VITE_TUS_ENDPOINT", "http://172.20.22.137:8080/files"),
    chunkSize: toNumber(getEnvValue("VITE_TUS_CHUNK_SIZE"), 20971520), // 20MB
    parallelUploads: toNumber(getEnvValue("VITE_TUS_PARALLEL_UPLOADS"), 3),
  },
  download: {
    endpoint: getEnvValue("VITE_DOWNLOAD_ENDPOINT", "http://172.20.22.137:8000"),
  },
  smartPacking: {
    threshold: toNumber(getEnvValue("VITE_SMART_PACKING_THRESHOLD"), 10), // 智能打包文件数量阈值，默认10个文件
  },
  dev: {
    enableMock: toBoolean(getEnvValue("VITE_ENABLE_MOCK")),
    enableDebug: toBoolean(getEnvValue("VITE_ENABLE_DEBUG")),
  },
};

/**
 * 检查是否为开发环境
 */
export const isDev = import.meta.env.DEV;

/**
 * 检查是否为生产环境
 */
export const isProd = import.meta.env.PROD;

/**
 * 获取当前模式
 */
export const mode = import.meta.env.MODE;

export default config;
