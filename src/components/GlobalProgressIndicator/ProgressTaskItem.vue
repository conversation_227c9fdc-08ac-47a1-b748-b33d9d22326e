<template>
  <div class="space-y-1">
    <div class="flex items-center justify-between text-xs">
      <div class="flex items-center flex-1 min-w-0 gap-2">
        <component :is="getTaskIcon()" :class="cn(
          'flex-shrink-0 w-3 h-3',
          getTaskIconClass()
        )" />
        <span class="font-medium truncate">{{ getTaskDisplayName() }}</span>
        <span v-if="task.size" class="flex-shrink-0 text-muted-foreground whitespace-nowrap">({{ task.size }})</span>
        <span v-if="task.status === 'error'" class="text-xs text-red-500 whitespace-nowrap">失败</span>
        <span v-if="task.status === 'paused'" class="text-xs text-yellow-500 whitespace-nowrap">已暂停</span>
        <!-- 压缩任务特有信息 -->
        <span v-if="task.type === 'archive' && task.status === 'in-progress'"
          class="text-xs text-blue-500 whitespace-nowrap">
          正在智能打包...
        </span>
      </div>
      <div class="flex items-center flex-shrink-0 gap-1">
        <span v-if="task.status !== 'error'" class="text-muted-foreground whitespace-nowrap">
          {{ Math.round(task.progress) }}%
        </span>

        <!-- 暂停按钮 (仅上传/下载中时显示，压缩任务不支持暂停) -->
        <Button v-if="task.status === 'in-progress' && task.type !== 'archive'" variant="ghost" size="sm"
          @click="$emit('pause', task.id)" class="w-5 h-5 p-0" title="暂停">
          <PauseIcon class="w-3 h-3" />
        </Button>

        <!-- 恢复按钮 (仅暂停时显示，压缩任务不支持暂停) -->
        <Button v-if="task.status === 'paused' && task.type !== 'archive'" variant="ghost" size="sm"
          @click="$emit('resume', task.id)" class="w-5 h-5 p-0" title="恢复">
          <PlayIcon class="w-3 h-3" />
        </Button>

        <!-- 重试按钮 -->
        <Button v-if="task.status === 'error'" variant="ghost" size="sm" @click="$emit('retry', task.id)"
          class="w-5 h-5 p-0" title="重试">
          <RefreshCwIcon class="w-3 h-3" />
        </Button>

        <!-- 取消/移除按钮 -->
        <Button variant="ghost" size="sm" @click="handleAction" class="w-5 h-5 p-0"
          :title="task.status === 'error' ? '移除' : '取消'">
          <XIcon class="w-3 h-3" />
        </Button>
      </div>
    </div>

    <!-- 进度条 -->
    <div v-if="task.status !== 'error'" class="w-full h-1 rounded-full bg-secondary">
      <div class="h-1 transition-all duration-300 rounded-full" :class="getProgressBarClass()"
        :style="{ width: `${task.progress}%` }" />
    </div>

    <!-- 压缩任务详情 -->
    <div v-if="task.type === 'archive' && task.status === 'in-progress'"
      class="text-xs text-muted-foreground space-y-1">
      <div v-if="task.currentFile" class="truncate">
        当前文件: {{ task.currentFile }}
      </div>
      <div v-if="task.totalFiles && task.processedFiles !== undefined" class="flex justify-between">
        <span>进度: {{ task.processedFiles }}/{{ task.totalFiles }} 个文件</span>
      </div>
    </div>

    <!-- 错误信息 -->
    <div v-if="task.error" class="p-1 text-xs text-red-500 rounded bg-red-50">
      {{ task.error }}
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  Upload as UploadIcon,
  Download as DownloadIcon,
  Archive as ArchiveIcon,
  AlertCircle as AlertCircleIcon,
  X as XIcon,
  RefreshCw as RefreshCwIcon,
  Pause as PauseIcon,
  Play as PlayIcon
} from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import type { ProgressTask } from '@/composables/useGlobalProgress'

interface Props {
  task: ProgressTask
}

const props = defineProps<Props>()

const emit = defineEmits<{
  cancel: [taskId: string]
  remove: [taskId: string]
  retry: [taskId: string]
  pause: [taskId: string]
  resume: [taskId: string]
}>()

const getTaskIcon = () => {
  if (props.task.status === 'error') return AlertCircleIcon
  if (props.task.status === 'paused') return PauseIcon
  if (props.task.type === 'upload') return UploadIcon
  if (props.task.type === 'download') return DownloadIcon
  if (props.task.type === 'archive') return ArchiveIcon
  return UploadIcon // 默认图标
}

const getTaskIconClass = () => {
  if (props.task.status === 'error') return 'text-red-500'
  if (props.task.status === 'paused') return 'text-yellow-500'
  return 'text-primary animate-pulse'
}

const getProgressBarClass = () => {
  if (props.task.status === 'paused') return 'bg-yellow-500'
  if (props.task.type === 'archive') return 'bg-blue-500'
  return 'bg-primary'
}

const getTaskDisplayName = () => {
  if (props.task.type === 'archive') {
    return props.task.fileName || '智能打包任务'
  }
  return props.task.fileName
}

const handleAction = () => {
  if (props.task.status === 'error') {
    emit('remove', props.task.id)
  } else {
    emit('cancel', props.task.id)
  }
}
</script>