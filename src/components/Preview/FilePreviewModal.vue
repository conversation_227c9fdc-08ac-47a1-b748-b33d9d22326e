<template>
  <Dialog :open="visible" @update:open="handleClose">
    <DialogContent class="max-w-6xl w-[90vw] p-0 overflow-hidden gap-4">
      <!-- 头部 -->
      <DialogHeader class="px-4 pt-4">
        <DialogTitle>
          {{ fileName || '文件预览' }}
        </DialogTitle>
      </DialogHeader>

      <!-- 预览内容 -->
      <div class="overflow-hidden flex-1">
        <!-- 不支持的文件类型 -->
        <div v-if="filePreview.detectedFileType.value === 'unsupported'"
          class="flex justify-center items-center p-8 h-full">
          <div class="text-center">
            <div class="flex justify-center items-center mx-auto mb-4 w-16 h-16 rounded-full bg-muted">
              <svg class="w-8 h-8 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 class="mb-2 font-medium text-foreground">暂不支持预览此文件类型</h3>
            <p class="mb-4 text-sm text-muted-foreground">
              当前仅支持图片、视频和音频文件的预览
            </p>
            <button v-if="filePreview.fullUrl.value" @click="downloadFile"
              class="px-4 py-2 rounded-md transition-colors bg-primary text-primary-foreground hover:bg-primary/90">
              下载文件
            </button>
          </div>
        </div>

        <!-- 图片预览 -->
        <ImagePreview v-else-if="filePreview.detectedFileType.value === 'image'" :image-url="filePreview.fullUrl.value"
          :file-name="fileName" :is-loading="filePreview.isLoading.value" :error="filePreview.error.value"
          @retry="handleRetry" />

        <!-- 视频预览 -->
        <VideoPreview v-else-if="filePreview.detectedFileType.value === 'video'" :video-url="filePreview.fullUrl.value"
          :file-name="fileName" :is-loading="filePreview.isLoading.value" :error="filePreview.error.value"
          @retry="handleRetry" />

        <!-- 音频预览 -->
        <AudioPreview v-else-if="filePreview.detectedFileType.value === 'audio'" :audio-url="filePreview.fullUrl.value"
          :file-name="fileName" :is-loading="filePreview.isLoading.value" :error="filePreview.error.value"
          @retry="handleRetry" />
      </div>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { watch, onMounted, onUnmounted } from 'vue'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { useFilePreview } from './composables/useFilePreview'
import ImagePreview from './ImagePreview.vue'
import VideoPreview from './VideoPreview.vue'
import AudioPreview from './AudioPreview.vue'

interface Props {
  visible: boolean
  previewUrl: string
  fileName?: string
  fileType?: string
}

interface Emits {
  (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  fileName: '',
  fileType: ''
})

const emit = defineEmits<Emits>()

// 使用文件预览组合式函数
const filePreview = useFilePreview({
  previewUrl: props.previewUrl,
  fileName: props.fileName,
  fileType: props.fileType
})

// 监听props变化，更新预览配置
watch(
  () => [props.previewUrl, props.fileName, props.fileType],
  ([newPreviewUrl, newFileName, newFileType]) => {
    // 重新创建filePreview实例
    Object.assign(filePreview, useFilePreview({
      previewUrl: newPreviewUrl,
      fileName: newFileName,
      fileType: newFileType
    }))
  }
)

// 监听弹窗显示状态
watch(() => props.visible, (visible) => {
  if (visible) {
    filePreview.clearError()
    // 可以在这里添加预加载逻辑
  }
})

/**
 * 处理关闭事件
 */
function handleClose(open: boolean) {
  if (!open) {
    emit('close')
  }
}


/**
 * 下载文件
 */
function downloadFile() {
  if (!filePreview.fullUrl.value) return

  const link = document.createElement('a')
  link.href = filePreview.fullUrl.value
  link.download = props.fileName || 'download'
  link.target = '_blank'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

/**
 * 重试加载
 */
function handleRetry() {
  filePreview.clearError()
  // 可以在这里添加重新加载逻辑
}



/**
 * 处理键盘事件
 */
function handleKeydown(event: KeyboardEvent) {
  if (event.key === 'Escape' && props.visible) {
    emit('close')
  }
}

// 添加键盘事件监听
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>
