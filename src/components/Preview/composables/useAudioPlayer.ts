import { ref, computed, watch, onMounted, onUnmounted, type Ref } from "vue";

export interface AudioPlayerOptions {
  audioUrl: string;
  autoPlay?: boolean;
  volume?: number;
}

export interface AudioPlayerState {
  audioRef: Ref<HTMLAudioElement | undefined>;
  isLoading: Ref<boolean>;
  isPlaying: Ref<boolean>;
  isPaused: Ref<boolean>;
  currentTime: Ref<number>;
  duration: Ref<number>;
  volume: Ref<number>;
  isMuted: Ref<boolean>;
  error: Ref<string | null>;
  progress: Ref<number>;
  buffered: Ref<number>;
  canPlay: Ref<boolean>;

  // 控制方法
  play: () => Promise<void>;
  pause: () => void;
  stop: () => void;
  togglePlay: () => Promise<void>;
  seek: (time: number) => void;
  setVolume: (volume: number) => void;
  toggleMute: () => void;
  skipForward: (seconds?: number) => void;
  skipBackward: (seconds?: number) => void;

  // 状态管理
  reset: () => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  setupAudioEvents: () => void;
}

/**
 * 音频播放器组合式函数
 * 提供完整的音频播放控制功能
 */
export function useAudioPlayer(options: AudioPlayerOptions): AudioPlayerState {
  // 音频元素引用
  const audioRef = ref<HTMLAudioElement>();

  // 播放状态
  const isLoading = ref(false);
  const isPlaying = ref(false);
  const isPaused = ref(false);
  const currentTime = ref(0);
  const duration = ref(0);
  const volume = ref(options.volume || 1);
  const isMuted = ref(false);
  const error = ref<string | null>(null);
  const canPlay = ref(false);

  // 缓冲进度
  const buffered = ref(0);

  // 计算播放进度百分比
  const progress = computed(() => {
    if (duration.value === 0) return 0;
    return (currentTime.value / duration.value) * 100;
  });

  // 监听URL变化，重置状态
  watch(
    () => options.audioUrl,
    () => {
      reset();
      if (audioRef.value) {
        audioRef.value.src = options.audioUrl;
        audioRef.value.load();
        // 确保每次URL变化时重新设置事件监听
        setupAudioEvents();
      }
    },
    { immediate: true }
  );

  /**
   * 播放音频
   */
  async function play(): Promise<void> {
    if (!audioRef.value) return;

    try {
      await audioRef.value.play();
    } catch (err) {
      console.error("播放失败:", err);
      setError("播放失败，请检查音频文件");
    }
  }

  /**
   * 暂停音频
   */
  function pause(): void {
    if (!audioRef.value) return;
    audioRef.value.pause();
  }

  /**
   * 停止音频
   */
  function stop(): void {
    if (!audioRef.value) return;
    audioRef.value.pause();
    audioRef.value.currentTime = 0;
  }

  /**
   * 切换播放/暂停
   */
  async function togglePlay(): Promise<void> {
    if (isPlaying.value) {
      pause();
    } else {
      await play();
    }
  }

  /**
   * 跳转到指定时间
   */
  function seek(time: number): void {
    if (!audioRef.value) return;
    audioRef.value.currentTime = Math.max(0, Math.min(time, duration.value));
  }

  /**
   * 设置音量
   */
  function setVolume(vol: number): void {
    if (!audioRef.value) return;
    const newVolume = Math.max(0, Math.min(1, vol));
    volume.value = newVolume;
    audioRef.value.volume = newVolume;
  }

  /**
   * 切换静音
   */
  function toggleMute(): void {
    if (!audioRef.value) return;
    audioRef.value.muted = !audioRef.value.muted;
  }

  /**
   * 快进
   */
  function skipForward(seconds: number = 10): void {
    seek(currentTime.value + seconds);
  }

  /**
   * 快退
   */
  function skipBackward(seconds: number = 10): void {
    seek(currentTime.value - seconds);
  }

  /**
   * 重置状态
   */
  function reset(): void {
    isLoading.value = false;
    isPlaying.value = false;
    isPaused.value = false;
    currentTime.value = 0;
    duration.value = 0;
    buffered.value = 0;
    canPlay.value = false;
    clearError();
  }

  /**
   * 设置错误信息
   */
  function setError(errorMessage: string | null): void {
    error.value = errorMessage;
  }

  /**
   * 清除错误
   */
  function clearError(): void {
    error.value = null;
  }

  /**
   * 处理音频事件
   */
  function setupAudioEvents(): void {
    if (!audioRef.value) return;

    const audio = audioRef.value;

    // 加载开始
    audio.addEventListener("loadstart", () => {
      isLoading.value = true;
      clearError();
    });

    // 可以播放
    audio.addEventListener("canplay", () => {
      isLoading.value = false;
      canPlay.value = true;
    });

    // 元数据加载完成
    audio.addEventListener("loadedmetadata", () => {
      duration.value = audio.duration || 0;
      volume.value = audio.volume;
      isMuted.value = audio.muted;
    });

    // 播放开始
    audio.addEventListener("play", () => {
      isPlaying.value = true;
      isPaused.value = false;
    });

    // 暂停
    audio.addEventListener("pause", () => {
      isPlaying.value = false;
      isPaused.value = true;
    });

    // 播放结束
    audio.addEventListener("ended", () => {
      isPlaying.value = false;
      isPaused.value = false;
      currentTime.value = 0;
    });

    // 时间更新
    audio.addEventListener("timeupdate", () => {
      currentTime.value = audio.currentTime;
    });

    // 音量变化
    audio.addEventListener("volumechange", () => {
      volume.value = audio.volume;
      isMuted.value = audio.muted;
    });

    // 缓冲进度更新
    audio.addEventListener("progress", () => {
      if (audio.buffered.length > 0) {
        const bufferedEnd = audio.buffered.end(audio.buffered.length - 1);
        buffered.value = duration.value > 0 ? (bufferedEnd / duration.value) * 100 : 0;
      }
    });

    // 错误处理
    audio.addEventListener("error", () => {
      isLoading.value = false;
      canPlay.value = false;
      setError("音频加载失败，请检查文件格式或网络连接");
    });

    // 等待数据
    audio.addEventListener("waiting", () => {
      isLoading.value = true;
    });

    // 可以继续播放
    audio.addEventListener("canplaythrough", () => {
      isLoading.value = false;
    });
  }

  /**
   * 键盘快捷键处理
   */
  function handleKeydown(event: KeyboardEvent): void {
    // 只在没有输入框聚焦时处理快捷键
    if (document.activeElement?.tagName === "INPUT" || document.activeElement?.tagName === "TEXTAREA") {
      return;
    }

    switch (event.code) {
      case "Space":
        event.preventDefault();
        togglePlay();
        break;
      case "ArrowLeft":
        event.preventDefault();
        skipBackward(5);
        break;
      case "ArrowRight":
        event.preventDefault();
        skipForward(5);
        break;
      case "ArrowUp":
        event.preventDefault();
        setVolume(volume.value + 0.1);
        break;
      case "ArrowDown":
        event.preventDefault();
        setVolume(volume.value - 0.1);
        break;
      case "KeyM":
        event.preventDefault();
        toggleMute();
        break;
    }
  }

  // 组件挂载时设置事件监听
  onMounted(() => {
    if (audioRef.value) {
      setupAudioEvents();
    }
    document.addEventListener("keydown", handleKeydown);
  });

  // 组件卸载时清理事件监听
  onUnmounted(() => {
    document.removeEventListener("keydown", handleKeydown);
  });

  return {
    audioRef,
    isLoading,
    isPlaying,
    isPaused,
    currentTime,
    duration,
    volume,
    isMuted,
    error,
    progress,
    buffered,
    canPlay,

    play,
    pause,
    stop,
    togglePlay,
    seek,
    setVolume,
    toggleMute,
    skipForward,
    skipBackward,

    reset,
    setError,
    clearError,
    setupAudioEvents,
  };
}

/**
 * 格式化时间显示
 */
export function formatTime(seconds: number): string {
  if (!isFinite(seconds)) return "00:00";

  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);

  return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
}
