import { ref, computed, type Ref } from "vue";
import { useUserStore } from "@/store/user";

export interface FilePreviewOptions {
  previewUrl: string;
  fileName?: string;
  fileType?: string;
}

export interface FilePreviewState {
  isLoading: Ref<boolean>;
  error: Ref<string | null>;
  fullUrl: Ref<string>;
  detectedFileType: Ref<"image" | "video" | "audio" | "unsupported">;
  setLoading: (loading: boolean) => void;
  setError: (errorMessage: string | null) => void;
  clearError: () => void;
  reset: () => void;
}

/**
 * 文件预览组合式函数
 * 处理URL拼接、文件类型判断、加载状态管理等
 */
export function useFilePreview(options: FilePreviewOptions): FilePreviewState {
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  // 拼接完整的预览URL
  const fullUrl = computed(() => {
    if (!options.previewUrl) return "";

    // 如果已经是完整URL，直接返回
    if (options.previewUrl.startsWith("http://") || options.previewUrl.startsWith("https://")) {
      return options.previewUrl;
    }

    const { getToken } = useUserStore();

    // 拼接baseURL
    const baseUrl = `${import.meta.env.VITE_PREVIEW_ENDPOINT}/netdisk/preview`;
    const previewPath = options.previewUrl.startsWith("/") ? options.previewUrl : `/${options.previewUrl}`;

    return `${baseUrl}?path=${previewPath}&token=${getToken()}`;
  });

  // 支持的图片格式
  const imageExtensions = ["jpg", "jpeg", "png", "gif", "webp", "svg", "bmp", "ico", "tiff"];

  // 支持的视频格式
  const videoExtensions = ["mp4", "webm", "ogg", "avi", "mov", "wmv", "flv", "mkv", "m4v"];

  // 支持的音频格式
  const audioExtensions = ["mp3", "wav", "flac", "aac", "ogg", "m4a", "wma", "opus"];

  // 图片MIME类型
  const imageMimeTypes = ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp", "image/svg+xml", "image/bmp", "image/x-icon", "image/tiff"];

  // 视频MIME类型
  const videoMimeTypes = ["video/mp4", "video/webm", "video/ogg", "video/avi", "video/quicktime", "video/x-msvideo", "video/x-flv", "video/x-matroska", "video/x-m4v"];

  // 音频MIME类型
  const audioMimeTypes = ["audio/mpeg", "audio/mp3", "audio/wav", "audio/flac", "audio/aac", "audio/ogg", "audio/mp4", "audio/x-m4a", "audio/x-ms-wma", "audio/opus"];

  // 检测文件类型 - 只在有预览URL时进行检测
  const detectedFileType = computed((): "image" | "video" | "audio" | "unsupported" => {
    // 如果没有预览URL，直接返回不支持
    if (!options.previewUrl) {
      return "unsupported";
    }

    // 优先使用传入的fileType
    if (options.fileType) {
      if (imageMimeTypes.includes(options.fileType.toLowerCase())) {
        return "image";
      }
      if (videoMimeTypes.includes(options.fileType.toLowerCase())) {
        return "video";
      }
      if (audioMimeTypes.includes(options.fileType.toLowerCase())) {
        return "audio";
      }
    }

    // 根据文件名扩展名判断
    if (options.fileName) {
      const extension = getFileExtension(options.fileName).toLowerCase();
      if (imageExtensions.includes(extension)) {
        return "image";
      }
      if (videoExtensions.includes(extension)) {
        return "video";
      }
      if (audioExtensions.includes(extension)) {
        return "audio";
      }
    }

    // 根据URL路径判断
    if (options.previewUrl) {
      const extension = getFileExtension(options.previewUrl).toLowerCase();
      if (imageExtensions.includes(extension)) {
        return "image";
      }
      if (videoExtensions.includes(extension)) {
        return "video";
      }
      if (audioExtensions.includes(extension)) {
        return "audio";
      }
    }

    // 有预览URL但无法识别格式时，默认当作图片处理
    return "image";
  });

  /**
   * 获取文件扩展名
   */
  function getFileExtension(filename: string): string {
    const lastDotIndex = filename.lastIndexOf(".");
    if (lastDotIndex === -1) return "";
    return filename.slice(lastDotIndex + 1);
  }

  /**
   * 设置加载状态
   */
  function setLoading(loading: boolean) {
    isLoading.value = loading;
  }

  /**
   * 设置错误信息
   */
  function setError(errorMessage: string | null) {
    error.value = errorMessage;
  }

  /**
   * 清除错误
   */
  function clearError() {
    error.value = null;
  }

  /**
   * 重置状态
   */
  function reset() {
    isLoading.value = false;
    error.value = null;
  }

  return {
    isLoading,
    error,
    fullUrl,
    detectedFileType,
    setLoading,
    setError,
    clearError,
    reset,
  };
}

/**
 * 检查文件是否支持预览 - 只检查是否有预览URL
 */
export function isSupportedFileType(previewUrl?: string): boolean {
  return !!previewUrl && previewUrl.trim() !== "";
}

/**
 * 检测文件类型用于选择预览组件
 */
export function detectFileType(fileName?: string, fileType?: string, previewUrl?: string): "image" | "video" | "audio" | "unsupported" {
  const { detectedFileType } = useFilePreview({
    previewUrl: previewUrl || "",
    fileName,
    fileType,
  });

  return detectedFileType.value;
}
