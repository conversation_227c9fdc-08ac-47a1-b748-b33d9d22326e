import { defineStore } from "pinia";
import { ref, computed } from "vue";
import { Folder } from "lucide-vue-next";
import api from "@/api";
import { useFolderConfig } from "@/composables/useFolderConfig";

// 接口返回的数据类型
export interface ApiCategoryItem {
  id: number;
  name: string;
  category_id: number;
  category_key: string;
  is_not_unpack?: "Y" | "N"; // 是否不需要自动解压缩，Y=不需要，N=需要（可选字段，向后兼容）
}

// 合并后的侧边栏项类型
export interface SidebarItem {
  id: number;
  name: string;
  category_id: number;
  category_key: string;
  path: string;
  icon: any;
  iconColor?: string;
  description?: string;
  is_not_unpack?: "Y" | "N"; // 是否不需要自动解压缩，Y=不需要，N=需要（可选字段，向后兼容）
}

// 使用 setup 语法定义 store
export const useSidebarStore = defineStore("sidebar", () => {
  // 获取文件夹配置
  const { FOLDER_CONFIGS } = useFolderConfig();

  // ==================== 状态 ====================

  // 原始的 API 数据
  const apiCategoryData = ref<ApiCategoryItem[]>([]);

  // 数据加载状态
  const isLoading = ref(false);
  const isLoaded = ref(false);

  // 错误状态
  const error = ref<string | null>(null);

  // ==================== 计算属性 ====================

  // 合并后的侧边栏数据
  const sidebarItems = computed<SidebarItem[]>(() => {
    return apiCategoryData.value.map((apiItem) => {
      const localConfig = FOLDER_CONFIGS[apiItem.category_key];

      return {
        ...apiItem,
        path: `/resources/${apiItem.category_key}`,
        icon: localConfig ? localConfig.icon : Folder,
        iconColor: localConfig ? localConfig.iconColor : "text-blue-400",
      };
    });
  });

  // ==================== Actions ====================

  /**
   * 获取顶级分类目录数据
   */
  const fetchSidebarData = async (force = false) => {
    // 如果已经加载过且不强制刷新，直接返回
    if (isLoaded.value && !force) {
      return sidebarItems.value;
    }

    isLoading.value = true;
    error.value = null;

    try {
      const res = await api.common.getTopCategoryDirectories();

      if (res.code === 0 && res.data) {
        apiCategoryData.value = res.data;
        isLoaded.value = true;
        return sidebarItems.value;
      } else {
        throw new Error(res.msg || "获取分类数据失败");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "网络请求失败";
      error.value = errorMessage;
      console.error("获取侧边栏数据失败:", err);
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  // ==================== 返回值 ====================

  return {
    // 状态
    apiCategoryData,
    isLoading,
    isLoaded,
    error,

    // 计算属性
    sidebarItems,

    // Actions
    fetchSidebarData,
  };
});
