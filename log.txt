📦 [渲染进程] 已调用 updateArchiveTaskProgress，当前任务列表: [{…}]
useArchiveProgress.ts:49 📦 [渲染进程] 收到压缩进度事件: archive_1753171409737_iqastoeh0 -> 95% ()
useArchiveProgress.ts:54 📦 [渲染进程] 已调用 updateArchiveTaskProgress，当前任务列表: [{…}]
useArchiveProgress.ts:49 📦 [渲染进程] 收到压缩进度事件: archive_1753171409737_iqastoeh0 -> 95% ()
useArchiveProgress.ts:54 📦 [渲染进程] 已调用 updateArchiveTaskProgress，当前任务列表: [{…}]
useArchiveProgress.ts:49 📦 [渲染进程] 收到压缩进度事件: archive_1753171409737_iqastoeh0 -> 95% ()
useArchiveProgress.ts:54 📦 [渲染进程] 已调用 updateArchiveTaskProgress，当前任务列表: [{…}]
useArchiveProgress.ts:49 📦 [渲染进程] 收到压缩进度事件: archive_1753171409737_iqastoeh0 -> 95% ()
useArchiveProgress.ts:54 📦 [渲染进程] 已调用 updateArchiveTaskProgress，当前任务列表: [{…}]
useArchiveProgress.ts:49 📦 [渲染进程] 收到压缩进度事件: archive_1753171409737_iqastoeh0 -> 95% ()
useArchiveProgress.ts:54 📦 [渲染进程] 已调用 updateArchiveTaskProgress，当前任务列表: [{…}]
useArchiveProgress.ts:49 📦 [渲染进程] 收到压缩进度事件: archive_1753171409737_iqastoeh0 -> 95% ()
useArchiveProgress.ts:54 📦 [渲染进程] 已调用 updateArchiveTaskProgress，当前任务列表: [{…}]
useArchiveProgress.ts:49 📦 [渲染进程] 收到压缩进度事件: archive_1753171409737_iqastoeh0 -> 100% (压缩完成)
useGlobalProgress.ts:1127 📦 更新压缩进度: 2G多UE工程文件.7z -> 100% (1603/1603) - 压缩完成
useArchiveProgress.ts:54 📦 [渲染进程] 已调用 updateArchiveTaskProgress，当前任务列表: []
useArchiveProgress.ts:71 📦 压缩任务完成: archive_1753171409737_iqastoeh0 {success: true, archivePath: '/var/folders/74/ypz2pg996kdb4q105z7g821c0000gn/T/clouddrive-archives/2G多UE工程文件.7z', stats: {…}}
useGlobalProgress.ts:1148 📦 压缩任务完成: 2G多UE工程文件.7z
useTusUpload.ts:84 [TUS-SHOWANALYSIS] 📦 智能打包上传API响应: {success: true, taskId: undefined, data: {…}, error: undefined}
useTusUpload.ts:84 [TUS-SHOWANALYSIS] 📦 智能打包上传成功启动，任务ID: archive_1753171409737_iqastoeh0
useTusUpload.ts:84 [TUS-SHOWANALYSIS] 📦 智能打包任务已创建，压缩进度将通过事件监听器自动显示: 2G多UE工程文件.7z (1605 个文件)
useTusUpload.ts:84 [TUS-SHOWEVENTS] 创建智能打包上传组: upload-group_1753171409637_vu9cvbihh, 包含 1605 个文件
useTusUpload.ts:84 [TUS-SHOWEVENTS] 任务创建事件 - 2G多UE工程文件.7z (upload_1753171414256_ckt072ehr)
useTusUpload.ts:84 [TUS-SHOWEVENTS] 智能打包任务已添加到上传组: upload-group_1753171409637_vu9cvbihh, 任务ID: upload_1753171414256_ckt072ehr
index.vue:64 📊 [GlobalProgressIndicator] activeTasks 变化: [{…}]
useTusUpload.ts:84 [TUS-SHOWTASKS] 任务状态变化: 2G多UE工程文件.7z -> uploading
index.vue:64 📊 [GlobalProgressIndicator] activeTasks 变化: [{…}]
index.vue:64 📊 [GlobalProgressIndicator] activeTasks 变化: [{…}]
index.vue:64 📊 [GlobalProgressIndicator] activeTasks 变化: [{…}]