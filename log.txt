📦 [渲染进程] 收到压缩进度事件: archive_1753171799533_w95cznffc -> 95% ()
useArchiveProgress.ts:54 📦 [渲染进程] 已调用 updateArchiveTaskProgress，当前任务列表: [{…}]
useArchiveProgress.ts:49 📦 [渲染进程] 收到压缩进度事件: archive_1753171799533_w95cznffc -> 95% ()
useArchiveProgress.ts:54 📦 [渲染进程] 已调用 updateArchiveTaskProgress，当前任务列表: [{…}]
useArchiveProgress.ts:49 📦 [渲染进程] 收到压缩进度事件: archive_1753171799533_w95cznffc -> 95% ()
useArchiveProgress.ts:54 📦 [渲染进程] 已调用 updateArchiveTaskProgress，当前任务列表: [{…}]
useArchiveProgress.ts:49 📦 [渲染进程] 收到压缩进度事件: archive_1753171799533_w95cznffc -> 95% ()
useArchiveProgress.ts:54 📦 [渲染进程] 已调用 updateArchiveTaskProgress，当前任务列表: [{…}]
useArchiveProgress.ts:49 📦 [渲染进程] 收到压缩进度事件: archive_1753171799533_w95cznffc -> 95% ()
useArchiveProgress.ts:54 📦 [渲染进程] 已调用 updateArchiveTaskProgress，当前任务列表: [{…}]
useArchiveProgress.ts:49 📦 [渲染进程] 收到压缩进度事件: archive_1753171799533_w95cznffc -> 95% ()
useArchiveProgress.ts:54 📦 [渲染进程] 已调用 updateArchiveTaskProgress，当前任务列表: [{…}]
useArchiveProgress.ts:49 📦 [渲染进程] 收到压缩进度事件: archive_1753171799533_w95cznffc -> 95% ()
useArchiveProgress.ts:54 📦 [渲染进程] 已调用 updateArchiveTaskProgress，当前任务列表: [{…}]
useArchiveProgress.ts:49 📦 [渲染进程] 收到压缩进度事件: archive_1753171799533_w95cznffc -> 95% ()
useArchiveProgress.ts:54 📦 [渲染进程] 已调用 updateArchiveTaskProgress，当前任务列表: [{…}]
useArchiveProgress.ts:49 📦 [渲染进程] 收到压缩进度事件: archive_1753171799533_w95cznffc -> 100% (压缩完成)
useGlobalProgress.ts:1139 📦 更新压缩进度: 2G多UE工程文件.7z -> 100% (1603/1603) - 压缩完成
useArchiveProgress.ts:54 📦 [渲染进程] 已调用 updateArchiveTaskProgress，当前任务列表: []
useArchiveProgress.ts:71 📦 压缩任务完成: archive_1753171799533_w95cznffc {success: true, archivePath: '/var/folders/74/ypz2pg996kdb4q105z7g821c0000gn/T/clouddrive-archives/2G多UE工程文件.7z', stats: {…}}archivePath: "/var/folders/74/ypz2pg996kdb4q105z7g821c0000gn/T/clouddrive-archives/2G多UE工程文件.7z"stats: {originalSize: 2352637706, compressedSize: 2352659601, compressionRatio: -0.0009306575315060073, fileCount: 1603, duration: 4481}compressedSize: 2352659601compressionRatio: -0.0009306575315060073duration: 4481fileCount: 1603originalSize: 2352637706[[Prototype]]: Objectsuccess: true[[Prototype]]: Object
useGlobalProgress.ts:1160 📦 压缩任务完成: 2G多UE工程文件.7z
useTusUpload.ts:84 [TUS-SHOWANALYSIS] 📦 智能打包上传API响应: {success: true, taskId: undefined, data: {…}, error: undefined}data: archiveTaskId: "archive_1753171799533_w95cznffc"[[Prototype]]: Objecterror: undefinedsuccess: truetaskId: undefined[[Prototype]]: Object
useTusUpload.ts:84 [TUS-SHOWANALYSIS] 📦 智能打包上传成功启动，任务ID: archive_1753171799533_w95cznffc
useTusUpload.ts:84 [TUS-SHOWANALYSIS] 📦 智能打包任务已创建，压缩进度将通过事件监听器自动显示: 2G多UE工程文件.7z (1605 个文件)
useTusUpload.ts:84 [TUS-SHOWEVENTS] 创建智能打包上传组: upload-group_1753171799453_wmq316gcj, 包含 1605 个文件
useTusUpload.ts:84 [TUS-SHOWEVENTS] 任务创建事件 - 2G多UE工程文件.7z (upload_1753171804076_xlmhig82m)
useTusUpload.ts:84 [TUS-SHOWEVENTS] 智能打包任务已添加到上传组: upload-group_1753171799453_wmq316gcj, 任务ID: upload_1753171804076_xlmhig82m