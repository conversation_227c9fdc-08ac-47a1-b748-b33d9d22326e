createTestArchiveTask()
useArchiveProgress.ts:158 🧪 创建测试压缩任务
useArchiveProgress.ts:163 🧪 调用 addArchiveTask: test_archive_1753172700050, 测试压缩包.7z, 100
useGlobalProgress.ts:1101 📦 创建压缩进度任务: 测试压缩包.7z (100 个文件)，任务ID: archive-test_archive_1753172700050
useGlobalProgress.ts:1102 📦 任务添加后，tasks.value 长度: 1
useGlobalProgress.ts:1103 📦 任务添加后，activeTasks 长度: 1
useGlobalProgress.ts:1104 📦 当前所有 activeTasks: [{…}]0: fileName: "测试压缩包.7z"id: "archive-test_archive_1753172700050"progress: 0status: "in-progress"type: "archive"[[Prototype]]: Objectlength: 1[[Prototype]]: Array(0)
useArchiveProgress.ts:165 🧪 addArchiveTask 返回的任务ID: archive-test_archive_1753172700050
'test_archive_1753172700050'
useArchiveProgress.ts:169 🧪 检查任务是否被添加到activeTasks:
useArchiveProgress.ts:170 activeTasks: [{…}]0: fileName: "测试压缩包.7z"id: "archive-test_archive_1753172700050"progress: 0status: "in-progress"type: "archive"[[Prototype]]: Objectlength: 1[[Prototype]]: Array(0)
useArchiveProgress.ts:184 🧪 模拟进度更新: 50%
useGlobalProgress.ts:1139 📦 更新压缩进度: 测试压缩包.7z -> 50% (50/100) - test-file.txt
useArchiveProgress.ts:189 🧪 模拟进度更新: 100%
useGlobalProgress.ts:1139 📦 更新压缩进度: 测试压缩包.7z -> 100% (100/100) - final-file.txt