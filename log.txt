createTestArchiveTask()
useArchiveProgress.ts:158 🧪 创建测试压缩任务
useArchiveProgress.ts:163 🧪 调用 addArchiveTask: test_archive_1753172821029, 测试压缩包.7z, 100
useGlobalProgress.ts:1101 📦 创建压缩进度任务: 测试压缩包.7z (100 个文件)，任务ID: archive-test_archive_1753172821029
useGlobalProgress.ts:1102 📦 任务添加后，tasks.value 长度: 1
useGlobalProgress.ts:1103 📦 任务添加后，activeTasks 长度: 1
useGlobalProgress.ts:1104 📦 当前所有 activeTasks: [{…}]0: fileName: "测试压缩包.7z"id: "archive-test_archive_1753172821029"progress: 0status: "in-progress"type: "archive"[[Prototype]]: Objectlength: 1[[Prototype]]: Array(0)
useArchiveProgress.ts:165 🧪 addArchiveTask 返回的任务ID: archive-test_archive_1753172821029
'test_archive_1753172821029'
useArchiveProgress.ts:169 🧪 检查任务是否被添加到activeTasks:
useArchiveProgress.ts:170 activeTasks: [{…}]0: fileName: "测试压缩包.7z"id: "archive-test_archive_1753172821029"progress: 0status: "in-progress"type: "archive"[[Prototype]]: Objectlength: 1[[Prototype]]: Array(0)
useArchiveProgress.ts:183 🧪 全局进度指示器元素: null
useArchiveProgress.ts:187 🧪 任务项元素数量: 0
useStreamDownloadManager.ts:438 设置下载事件监听器
useStreamDownloadManager.ts:447 下载事件监听器设置完成
useStreamDownloadManager.ts:1341 🔄 等待 Electron 模块就绪...
useStreamDownloadManager.ts:478 🔄 检查下载模块是否已经就绪...
useGlobalProgress.ts:203 📚 历史记录初始化完成:
useGlobalProgress.ts:204   - 上传历史: 0 条
useGlobalProgress.ts:205   - 下载历史: 0 条
useGlobalProgress.ts:206   - 上传批量历史: 0 条
useGlobalProgress.ts:207   - 下载批量历史: 0 条
useArchiveProgress.ts:191 🧪 hasActiveTasks: false
useStreamDownloadManager.ts:482 ✅ 下载模块已经就绪，无需等待
useStreamDownloadManager.ts:1344 📥 开始恢复已存在的下载任务...
useStreamDownloadManager.ts:525 从 Electron 端获取到 0 个任务
useStreamDownloadManager.ts:572 ✅ 任务恢复完成: 恢复 0 个未完成任务，跳过 0 个已完成任务
useStreamDownloadManager.ts:1349 ✅ 下载管理器初始化完成
useArchiveProgress.ts:196 🧪 模拟进度更新: 50%
useGlobalProgress.ts:1139 📦 更新压缩进度: 测试压缩包.7z -> 50% (50/100) - test-file.txt
useArchiveProgress.ts:201 🧪 模拟进度更新: 100%
useGlobalProgress.ts:1139 📦 更新压缩进度: 测试压缩包.7z -> 100% (100/100) - final-file.txt