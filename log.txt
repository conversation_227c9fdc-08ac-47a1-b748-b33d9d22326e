[TUS-SHOWANALYSIS] 📦 调用智能打包上传API，文件数: 1605
useArchiveProgress.ts:37 📦 [渲染进程] 收到压缩任务创建事件: 2G多UE工程文件 (ID: archive_1753172129106_k1iat0pq4) {id: 'archive_1753172129106_k1iat0pq4', name: '2G多UE工程文件', sourcePaths: Array(1605), outputPath: '/var/folders/74/ypz2pg996kdb4q105z7g821c0000gn/T/clouddrive-archives/2G多UE工程文件.7z', status: 'pending', …}
useArchiveProgress.ts:43 📦 [渲染进程] 准备创建前端进度任务: 2G多UE工程文件.7z, 总文件数: 1603
useGlobalProgress.ts:1101 📦 创建压缩进度任务: 2G多UE工程文件.7z (1603 个文件)，任务ID: archive-archive_1753172129106_k1iat0pq4
useGlobalProgress.ts:1102 📦 任务添加后，tasks.value 长度: 1
useGlobalProgress.ts:1103 📦 任务添加后，activeTasks 长度: 1
useGlobalProgress.ts:1104 📦 当前所有 activeTasks: [{…}]0: fileName: "2G多UE工程文件.7z"id: "archive-archive_1753172129106_k1iat0pq4"progress: 0status: "in-progress"type: "archive"[[Prototype]]: Objectlength: 1[[Prototype]]: Array(0)
useArchiveProgress.ts:45 📦 [渲染进程] 前端进度任务创建完成，ID: archive-archive_1753172129106_k1iat0pq4
useArchiveProgress.ts:50 📦 [渲染进程] 收到压缩进度事件: archive_1753172129106_k1iat0pq4 -> 0% (开始压缩...)
useGlobalProgress.ts:1139 📦 更新压缩进度: 2G多UE工程文件.7z -> 0% (0/1603) - 开始压缩...
useArchiveProgress.ts:64 📦 [渲染进程] 已调用 updateArchiveTaskProgress，当前任务列表: [{…}]
FolderView.vue:307 目录内容加载完成 (原因: 上传成功)
useArchiveProgress.ts:50 📦 [渲染进程] 收到压缩进度事件: archive_1753172129106_k1iat0pq4 -> 0.5% ()
useArchiveProgress.ts:64 📦 [渲染进程] 已调用 updateArchiveTaskProgress，当前任务列表: [{…}]
useArchiveProgress.ts:50 📦 [渲染进程] 收到压缩进度事件: archive_1753172129106_k1iat0pq4 -> 1% ()