筛选选项发生变化，重新初始化选中值
FolderView.vue:307 目录内容加载完成 (原因: 路由变化)
FolderView.vue:470 📁 拖拽上传: 接收到 1605 个文件，自动打开上传弹窗
UploadDialog.vue:180 📁 UploadDialog: 设置初始文件 1605 个
UploadDialog.vue:186 初始文件上传策略: {type: 'folder', folders: Array(1), singleFiles: Array(0), description: '单文件夹上传: 2G多UE工程文件 (1605个文件)'}
FileUploadArea.vue:468 📁 FileUploadArea: 接收到外部预设文件 1605 个
useFileUpload.ts:109 📁 processFiles: 开始处理 1605 个文件
useFileUpload.ts:114 📁 文件 1: 2G多UE工程文件/.DS_Store (6148 bytes)
useFileUpload.ts:114 📁 文件 2: 2G多UE工程文件/Config/DefaultEditor.ini (49 bytes)
useFileUpload.ts:114 📁 文件 3: 2G多UE工程文件/Config/DefaultEngine.ini (2518 bytes)
useFileUpload.ts:114 📁 文件 4: 2G多UE工程文件/Config/DefaultGame.ini (252 bytes)
useFileUpload.ts:114 📁 文件 5: 2G多UE工程文件/Config/DefaultInput.ini (10106 bytes)
useFileUpload.ts:133 📁 文件去重: 原始 1605 个文件，去重后 1605 个文件
useFileUpload.ts:195 📁 最终添加 1605 个新文件，当前总计 1605 个文件
UploadDialog.vue:217 上传策略更新: {type: 'folder', folders: Array(1), singleFiles: Array(0), description: '单文件夹上传: 2G多UE工程文件 (1605个文件)'}
FolderView.vue:481 上传数据: {files: Proxy(Array), attributes: {…}}
useTusUpload.ts:84 [TUS-SHOWANALYSIS] 📊 上传分析: 1605 个文件
useTusUpload.ts:84 [TUS-SHOWANALYSIS] 📁 文件夹数: 1
useTusUpload.ts:84 [TUS-SHOWANALYSIS] 📄 单独文件数: 0
useTusUpload.ts:84 [TUS-SHOWANALYSIS] 🔀 混合上传: 否
useTusUpload.ts:84 [TUS-SHOWANALYSIS] 📦 将创建 1 个独立上传任务
useTusUpload.ts:84 [TUS-SHOWANALYSIS] 📦 检测到大量文件 (1605 >= 10)，进行智能打包分析
useTusUpload.ts:84 [TUS-SHOWANALYSIS] 📦 获取到文件路径: 1605 个，示例: /Users/<USER>/Downloads/测试/2G多UE工程文件/.DS_Store, /Users/<USER>/Downloads/测试/2G多UE工程文件/Config/DefaultEditor.ini, /Users/<USER>/Downloads/测试/2G多UE工程文件/Config/DefaultEngine.ini
useTusUpload.ts:84 [TUS-SHOWANALYSIS] 📦 智能打包建议: 文件数量 1603 超过阈值 10，建议打包上传
useTusUpload.ts:84 [TUS-SHOWANALYSIS] 📦 智能打包默认启用，开始打包上传
useTusUpload.ts:84 [TUS-SHOWANALYSIS] 📦 调用智能打包上传API，文件数: 1605
FolderView.vue:307 目录内容加载完成 (原因: 上传成功)
useStreamDownloadManager.ts:438 设置下载事件监听器
useStreamDownloadManager.ts:447 下载事件监听器设置完成
useStreamDownloadManager.ts:1341 🔄 等待 Electron 模块就绪...
useStreamDownloadManager.ts:478 🔄 检查下载模块是否已经就绪...
useStreamDownloadManager.ts:482 ✅ 下载模块已经就绪，无需等待
useStreamDownloadManager.ts:1344 📥 开始恢复已存在的下载任务...
useStreamDownloadManager.ts:525 从 Electron 端获取到 5 个任务
useStreamDownloadManager.ts:546 ✅ 跳过已完成任务: .DS_Store (状态: completed)
useStreamDownloadManager.ts:546 ✅ 跳过已完成任务: 高清视频素材 ≥8k(7680px) 免费下载 - 爱给网.mp4 (状态: completed)
useStreamDownloadManager.ts:546 ✅ 跳过已完成任务: 视频.mp4 (状态: completed)
useStreamDownloadManager.ts:546 ✅ 跳过已完成任务: Squid_Game_2021_S03E01.mp4 (状态: completed)
useStreamDownloadManager.ts:546 ✅ 跳过已完成任务: Squid_Game_2021_S03E02.mp4 (状态: completed)
useStreamDownloadManager.ts:572 ✅ 任务恢复完成: 恢复 0 个未完成任务，跳过 5 个已完成任务
useStreamDownloadManager.ts:1349 ✅ 下载管理器初始化完成
useTusUpload.ts:84 [TUS-SHOWANALYSIS] 📦 智能打包上传API响应: {success: true, taskId: undefined, data: {…}, error: undefined}data: {archiveTaskId: 'archive_1753169009833_hnvw9v8iz'}error: undefinedsuccess: truetaskId: undefined[[Prototype]]: Object
useTusUpload.ts:84 [TUS-SHOWANALYSIS] 📦 智能打包上传成功启动，任务ID: archive_1753169009833_hnvw9v8iz
useTusUpload.ts:84 [TUS-SHOWANALYSIS] 📦 智能打包任务已创建，压缩进度将通过事件监听器自动显示: 2G多UE工程文件.7z (1605 个文件)
useTusUpload.ts:84 [TUS-SHOWEVENTS] 创建智能打包上传组: upload-group_1753169009734_rt63z9r73, 包含 1605 个文件
useTusUpload.ts:84 [TUS-SHOWEVENTS] 任务创建事件 - 2G多UE工程文件.7z (upload_1753169014349_qjif5zpac)
useTusUpload.ts:84 [TUS-SHOWEVENTS] 智能打包任务已添加到上传组: upload-group_1753169009734_rt63z9r73, 任务ID: upload_1753169014349_qjif5zpac
useTusUpload.ts:84 [TUS-SHOWTASKS] 任务状态变化: 2G多UE工程文件.7z -> uploading