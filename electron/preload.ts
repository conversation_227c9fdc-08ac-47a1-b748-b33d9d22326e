import { contextBridge, ipcRenderer } from "electron";
import { createSimplifiedTusPreloadApi } from "./tus/preloadApi";
import { createSimplifiedDownloadPreloadApi } from "./stream-downloader/preloadApi";
import { createSimplifiedArchivePreloadApi } from "./archive/preloadApi";
import { createSimplifiedExtractionPreloadApi } from "./7z-extractor/preloadApi";

// 从 TUS 模块导出类型，保持向后兼容性
export type { UploadTask, TusUploadConfig } from "./tus/types";

// 从下载模块导出类型
export type { DownloadTask, StreamDownloadConfig } from "./stream-downloader/types";

// 从压缩模块导出类型
export type { ArchiveTask, ArchiveConfig } from "./archive/types";

// 从解压缩模块导出类型
export type { ExtractionTask, ExtractionConfig } from "./7z-extractor/types";

// 创建简化的 TUS API（整合了 API 调用和事件监听器）
const tusApi = createSimplifiedTusPreloadApi();

// 创建简化的下载 API（整合了 API 调用和事件监听器）
const downloadApi = createSimplifiedDownloadPreloadApi();

// 创建简化的压缩 API（整合了 API 调用和事件监听器）
const archiveApi = createSimplifiedArchivePreloadApi();

// 创建简化的解压缩 API（整合了 API 调用和事件监听器）
const extractionApi = createSimplifiedExtractionPreloadApi();

// 渲染进程的自定义 API
const api = {
  // 应用信息
  getAppVersion: () => ipcRenderer.invoke("get-app-version"),
  getPlatform: () => ipcRenderer.invoke("get-platform"),

  // 对话框 API
  showOpenDialog: (options: Electron.OpenDialogOptions) => ipcRenderer.invoke("show-open-dialog", options),
  showSaveDialog: (options: Electron.SaveDialogOptions) => ipcRenderer.invoke("show-save-dialog", options),
  showMessageBox: (options: Electron.MessageBoxOptions) => ipcRenderer.invoke("show-message-box", options),

  // 文件系统 API
  getFileInfo: (filePath: string) => ipcRenderer.invoke("get-file-info", filePath),

  // 拖拽文件处理 API
  dragDrop: {
    handleFileDrop: (filePaths: string[]) => ipcRenderer.invoke("drag-drop-handle-files", filePaths),
  },

  // 文件夹选择 API
  folderSelect: {
    selectFolderAndGetFiles: () => ipcRenderer.invoke("folder-select-and-get-files"),
  },

  // TUS 上传 API（整合了 API 调用和事件监听器）
  tus: tusApi,

  // StreamSaver 下载 API（整合了 API 调用和事件监听器）
  download: downloadApi,

  // Archive 压缩 API（整合了 API 调用和事件监听器）
  archive: archiveApi,

  // 7z 解压缩 API（整合了 API 调用和事件监听器）
  extraction: extractionApi,

  // 主进程消息监听器
  onMainProcessMessage: (callback: (data: string) => void) => {
    ipcRenderer.on("main-process-message", (_event, data) => callback(data));
  },

  // 模块就绪通知
  onModulesReady: (callback: () => void) => {
    ipcRenderer.on("modules-ready", () => callback());
  },

  // 认证相关 API
  onAuthToken: (callback: (token: string) => void) => {
    ipcRenderer.on("auth-token", (_event, token) => callback(token));
  },

  clearAuthState: () => {
    ipcRenderer.send("clear-auth-state");
  },
};

// 使用 `contextBridge` API 向渲染进程暴露 Electron API
// 仅在启用上下文隔离时有效，否则直接添加到 DOM 全局对象
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld("electronAPI", api);
  } catch (error) {
    console.error(error);
  }
} else {
  // @ts-ignore (在 dts 文件中定义)
  window.electronAPI = api;
}
