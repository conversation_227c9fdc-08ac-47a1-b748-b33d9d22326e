/**
 * TUS 上传模块主入口
 *
 * 这个模块封装了所有与 TUS 上传相关的功能，包括：
 * - 上传管理器 (TusUploadManager)
 * - IPC 处理器 (IPC Handlers)
 * - Preload API (Preload API)
 * - 类型定义 (Types)
 * - 事件转发 (Event Forwarding)
 */

// 导入需要的类型和类
import type { BrowserWindow } from "electron";
import { TusUploadManager } from "./uploadManager";
import { registerTusIpcHandlers, unregisterTusIpcHandlers } from "./ipcHandlers";
import type { TusUploadConfig } from "./types";

// 导出核心类和类型
export { TusUploadManager } from "./uploadManager";
export type { UploadTask, TusUploadConfig, UploadStatus, UploadEvents, ApiResponse, StoreData } from "./types";

// 导出 IPC 处理器
export { registerTusIpcHandlers, unregisterTusIpcHandlers } from "./ipcHandlers";

// 导出 Preload API
export { createTusApi, createTusEventListeners, createSimplifiedTusPreloadApi } from "./preloadApi";
export type { TusPreloadApi, TusEventListeners, SimplifiedTusPreloadApi } from "./preloadApi";

/**
 * 创建默认的 TUS 配置
 * @note 认证头由uploadManager动态添加，无需在配置中指定
 */
export function createDefaultTusConfig(endpoint?: string): TusUploadConfig {
  return {
    endpoint: endpoint || process.env.TUS_ENDPOINT || "http://localhost:1080/files",
    chunkSize: 20 * 1024 * 1024, // 20MB
    retryDelays: [0, 1000, 3000, 5000],
    parallelUploads: 3,
    headers: {
      // 认证头由uploadManager在onBeforeRequest中动态添加
      // 这里可以添加其他静态头信息
    },
  };
}

/**
 * TUS 模块工厂函数
 * 用于在主进程中初始化完整的 TUS 上传系统
 */
export function createTusModule(config?: Partial<TusUploadConfig>) {
  const finalConfig = {
    ...createDefaultTusConfig(),
    ...config,
  };

  const uploadManager = new TusUploadManager(finalConfig);

  return {
    uploadManager,
    registerHandlers: () => registerTusIpcHandlers(uploadManager),
    unregisterHandlers: unregisterTusIpcHandlers,
    config: finalConfig,
  };
}

/**
 * 设置事件转发器
 * 将上传管理器的事件转发到渲染进程
 */
function setupEventForwarding(uploadManager: TusUploadManager, mainWindow: BrowserWindow) {
  // 监听上传事件并转发给渲染进程
  uploadManager.on("task-created", (taskId: string, task: any) => {
    mainWindow?.webContents.send("upload-task-created", taskId, task);
  });

  uploadManager.on("task-progress", (taskId: string, progress: number, bytesUploaded: number, bytesTotal: number) => {
    mainWindow?.webContents.send("upload-task-progress", taskId, progress, bytesUploaded, bytesTotal);
  });

  uploadManager.on("task-status-changed", (taskId: string, status: string, error?: string) => {
    mainWindow?.webContents.send("upload-task-status-changed", taskId, status, error);
  });

  uploadManager.on("task-completed", (taskId: string) => {
    mainWindow?.webContents.send("upload-task-completed", taskId);
  });

  uploadManager.on("task-error", (taskId: string, error: string) => {
    mainWindow?.webContents.send("upload-task-error", taskId, error);
  });
}

/**
 * 高级 TUS 模块初始化函数
 * 为主进程提供一键式 TUS 上传系统初始化
 *
 * @param mainWindow 主窗口实例，用于事件转发
 * @param config 可选的 TUS 配置
 * @returns TUS 模块实例和清理函数
 */
export function initializeTusModule(mainWindow: BrowserWindow, config?: Partial<TusUploadConfig>) {
  // 创建 TUS 模块
  const tusModule = createTusModule(config);
  const { uploadManager } = tusModule;

  // 注册 IPC 处理器
  tusModule.registerHandlers();

  // 设置事件转发
  setupEventForwarding(uploadManager, mainWindow);

  // 返回模块实例和清理函数
  return {
    uploadManager,
    config: tusModule.config,

    /**
     * 清理 TUS 模块资源
     */
    cleanup: () => {
      tusModule.unregisterHandlers();
      uploadManager.removeAllListeners();
    },
  };
}
