# TUS 上传模块

这个模块包含了所有与 TUS 上传相关的功能，提供了模块化、类型安全的上传解决方案。

## 模块结构

```
electron/tus/
├── types.ts           # 类型定义
├── uploadManager.ts   # 上传管理器核心逻辑
├── ipcHandlers.ts     # IPC 处理器
├── preloadApi.ts      # Preload API
├── index.ts           # 主入口文件
└── README.md          # 文档
```

## 主要组件

### 1. TusUploadManager (uploadManager.ts)
核心上传管理器，负责：
- 管理上传任务的生命周期
- 处理文件上传的状态和进度
- 提供事件通知机制
- 持久化存储上传状态

### 2. IPC 处理器 (ipcHandlers.ts)
主进程的 IPC 处理器，负责：
- 注册所有 TUS 相关的 IPC 处理函数
- 提供统一的错误处理
- 类型安全的 API 接口

### 3. Preload API (preloadApi.ts)
渲染进程的 API 接口，负责：
- 提供类型安全的 API 调用
- 封装事件监听器
- 简化渲染进程的使用

### 4. 类型定义 (types.ts)
统一的类型定义，包括：
- UploadTask: 上传任务接口
- TusUploadConfig: 配置接口
- ApiResponse: API 响应接口
- 其他相关类型

## 使用方式

### 在主进程中使用（推荐方式）

```typescript
import { initializeTusModule } from './tus/index';

// 一键式初始化 TUS 模块
const tusModule = initializeTusModule(mainWindow, {
  endpoint: 'http://localhost:1080/files',
  chunkSize: 20 * 1024 * 1024,
  retryDelays: [0, 1000, 3000, 5000],
  parallelUploads: 3,
});

// 应用退出时清理资源
app.on('window-all-closed', () => {
  tusModule.cleanup();
});
```

### 在主进程中使用（手动方式）

```typescript
import { createTusModule } from './tus/index';

// 创建 TUS 模块
const tusModule = createTusModule({
  endpoint: 'http://localhost:1080/files',
  chunkSize: 20 * 1024 * 1024,
});

// 注册 IPC 处理器
tusModule.registerHandlers();

// 监听上传事件
const { uploadManager } = tusModule;
uploadManager.on('task-created', (taskId, task) => {
  console.log('任务创建:', taskId, task);
});
```

### 在 Preload 脚本中使用（推荐方式）

```typescript
import { createSimplifiedTusPreloadApi } from './tus/index';

// 创建简化的 TUS API（整合了 API 调用和事件监听器）
const tusApi = createSimplifiedTusPreloadApi();

// 暴露给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  tus: tusApi,
  // ... 其他 API
});
```

### 在 Preload 脚本中使用（手动方式）

```typescript
import { createTusApi, createTusEventListeners } from './tus/index';

const tusApi = createTusApi();
const tusEventListeners = createTusEventListeners();

// 暴露给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  tus: tusApi,
  onUploadTaskCreated: tusEventListeners.onUploadTaskCreated,
  // ... 其他 API
});
```

### 在渲染进程中使用

```typescript
// 创建上传任务
const result = await window.electronAPI.tus.createUpload('/path/to/file');
if (result.success) {
  // 开始上传
  await window.electronAPI.tus.startUpload(result.taskId);
}

// 监听上传进度（使用简化 API）
window.electronAPI.tus.onUploadTaskProgress((taskId, progress, bytesUploaded, bytesTotal) => {
  console.log(`任务 ${taskId} 进度: ${progress}%`);
});

// 监听上传完成
window.electronAPI.tus.onUploadTaskCompleted((taskId) => {
  console.log(`任务 ${taskId} 已完成`);
});
```

## 大文件上传最佳实践

### 📋 文件大小指导原则

- **小文件 (< 50MB)**: 可以使用任何方式上传
- **大文件 (50MB - 100MB)**: 建议使用文件选择对话框
- **超大文件 (> 100MB)**: 强烈建议使用文件选择对话框

### 🚀 推荐方案 1：使用文件选择对话框（零内存问题）

```typescript
// 在渲染进程中使用文件选择对话框上传（推荐）
const { createUploadFromDialog, startUpload } = useTusUpload();

// 方法 1：使用文件选择对话框（推荐用于大文件）
const uploadLargeFiles = async () => {
  try {
    // 弹出文件选择对话框，用户选择文件后直接在主进程创建上传任务
    const taskIds = await createUploadFromDialog();
    
    console.log(`创建了 ${taskIds.length} 个上传任务`);
    
    // 开始所有上传任务
    for (const taskId of taskIds) {
      await startUpload(taskId);
    }
  } catch (error) {
    console.error("上传失败:", error);
  }
};
```

### 💡 推荐方案 2：智能文件处理（自动优化）

```typescript
// 方法 2：智能文件处理（自动选择最佳方式）
const { smartUploadFiles, startUpload, recommendUploadMethod } = useTusUpload();

const handleFilesDrop = async (files: File[]) => {
  try {
    // 显示文件大小建议
    files.forEach(file => {
      console.log(`${file.name}: ${recommendUploadMethod(file.size)}`);
    });

    // 自动判断文件大小和路径信息，选择最佳上传方式
    const taskIds = await smartUploadFiles(files);
    
    for (const taskId of taskIds) {
      await startUpload(taskId);
    }
  } catch (error) {
    // 如果有大文件无法处理，建议用户使用文件选择对话框
    console.error("部分文件无法处理:", error.message);
    alert("部分大文件无法处理，请使用文件选择按钮重新选择文件");
  }
};
```

### 📁 方案 3：文件路径批量上传

```typescript
// 方法 3：从文件路径批量上传（适用于已知文件路径）
const { createUploadsFromPaths, startUpload } = useTusUpload();

const uploadByPaths = async (filePaths: string[]) => {
  try {
    const taskIds = await createUploadsFromPaths(filePaths, {
      category: "batch-upload",
      source: "file-paths"
    });
    
    for (const taskId of taskIds) {
      await startUpload(taskId);
    }
  } catch (error) {
    console.error("批量上传失败:", error);
  }
};
```

### 传统方案：拖拽/点击上传（小文件适用）

```typescript
// 方法 4：从 File 对象创建（优化后，自动处理路径信息）
const uploadFiles = async (files: File[]) => {
  const { createUploadFromFile, startUpload } = useTusUpload();
  
  for (const file of files) {
    try {
      // 优化后的方法会自动检查文件路径信息
      // 如果有路径信息，直接使用路径上传（避免内存问题）
      // 如果没有路径信息且文件过大，会抛出错误并建议使用文件选择对话框
      const taskId = await createUploadFromFile(file);
      await startUpload(taskId);
    } catch (error) {
      console.error(`上传 ${file.name} 失败:`, error);
      
      if (error.message.includes("请使用文件选择对话框")) {
        // 大文件建议使用文件选择对话框
        alert(`文件 ${file.name} 过大，请使用文件选择按钮重新上传`);
      }
    }
  }
};
```

### 🔧 核心优化特性

1. **智能路径检测**: 自动检查 File 对象的 `path` 属性，优先使用文件路径而非读取内容
2. **大文件保护**: 超过 50MB 的文件如果没有路径信息会提示使用文件选择对话框
3. **零内存上传**: 文件选择对话框方式完全在主进程处理，渲染进程零内存占用
4. **智能回退**: 小文件没有路径信息时仍支持内容上传
5. **批量优化**: 支持文件路径数组批量创建上传任务

### ⚠️ 重要注意事项

1. **Electron 环境**: 通过文件输入或拖拽获得的 File 对象在 Electron 中通常包含 `path` 属性
2. **内存管理**: 避免在渲染进程中调用 `file.arrayBuffer()` 处理大文件
3. **用户体验**: 为大文件提供文件选择对话框选项，避免界面卡死
4. **错误处理**: 优雅处理大文件上传失败，引导用户使用正确的上传方式

## API 方法

- `createUpload()`: 从文件路径创建上传任务
- `createUploadFromFile()`: 从 File 对象创建上传任务（小文件适用）
- `createUploadFromDialog()`: 通过文件选择对话框创建上传任务（推荐）
- `startUpload()`: 开始上传
- `pauseUpload()`: 暂停上传
- `resumeUpload()`: 恢复上传
- `cancelUpload()`: 取消上传
- `retryUpload()`: 重试上传
- `deleteTask()`: 删除任务
- `getAllTasks()`: 获取所有任务
- `getTask()`: 获取指定任务
- `getActiveTasks()`: 获取活跃任务
- `updateConfig()`: 更新配置
- `clearCompletedTasks()`: 清理已完成任务

## 简化的事件监听方法（使用 SimplifiedTusPreloadApi）

- `onUploadTaskCreated()`: 监听任务创建
- `onUploadTaskProgress()`: 监听上传进度
- `onUploadTaskStatusChanged()`: 监听状态变更
- `onUploadTaskCompleted()`: 监听上传完成
- `onUploadTaskError()`: 监听上传错误
- `removeAllListeners()`: 移除所有监听器

## 特性

- **模块化设计**: 清晰的模块分离，易于维护和扩展
- **类型安全**: 完整的 TypeScript 类型支持
- **事件驱动**: 基于事件的状态管理
- **错误处理**: 统一的错误处理机制
- **持久化**: 自动保存和恢复上传状态
- **可配置**: 灵活的配置选项
- **一键初始化**: 提供简化的初始化函数
- **整合 API**: 简化的 Preload API，整合了调用和事件监听

## 配置选项

```typescript
interface TusUploadConfig {
  endpoint: string;              // TUS 服务器端点
  chunkSize?: number;           // 分片大小 (默认 20MB)
  retryDelays?: number[];       // 重试延迟数组
  parallelUploads?: number;     // 并行上传数量
  metadata?: Record<string, string>; // 默认元数据
  headers?: Record<string, string>;  // 默认请求头
}
```

## 事件列表

- `task-created`: 任务创建
- `task-progress`: 上传进度更新
- `task-status-changed`: 状态变更
- `task-completed`: 上传完成
- `task-error`: 上传错误

## API 方法

- `createUpload()`: 创建上传任务
- `startUpload()`: 开始上传
- `pauseUpload()`: 暂停上传
- `resumeUpload()`: 恢复上传
- `cancelUpload()`: 取消上传
- `retryUpload()`: 重试上传
- `deleteTask()`: 删除任务
- `getAllTasks()`: 获取所有任务
- `getTask()`: 获取指定任务
- `getActiveTasks()`: 获取活跃任务
- `updateConfig()`: 更新配置
- `clearCompletedTasks()`: 清理已完成任务

## 简化的事件监听方法（使用 SimplifiedTusPreloadApi）

- `onUploadTaskCreated()`: 监听任务创建
- `onUploadTaskProgress()`: 监听上传进度
- `onUploadTaskStatusChanged()`: 监听状态变更
- `onUploadTaskCompleted()`: 监听上传完成
- `onUploadTaskError()`: 监听上传错误
- `removeAllListeners()`: 移除所有监听器 