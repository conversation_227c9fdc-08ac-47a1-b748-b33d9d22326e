/**
 * Archive 模块主入口文件
 * 提供压缩和智能打包功能
 */

import type { BrowserWindow } from "electron";
import { ArchiveManager } from "./archiveManager";
import { registerArchiveIpcHandlers, unregisterArchiveIpcHandlers } from "./ipcHandlers";
import type { ArchiveConfig } from "./types";
import { logger } from "../logger";

// 导出核心类和类型
export { ArchiveManager } from "./archiveManager";
export type { ArchiveTask, ArchiveConfig, ArchiveOptions, ArchiveResult, ArchiveStatus, ArchiveEvents, ArchiveApiResponse, SmartPackingAnalysis, BatchPackingOptions } from "./types";

// 导出 IPC 处理器
export { registerArchiveIpcHandlers, unregisterArchiveIpcHandlers } from "./ipcHandlers";

// 导出 Preload API
export { createArchiveApi, createArchiveEventListeners, createSimplifiedArchivePreloadApi } from "./preloadApi";
export type { ArchivePreloadApi, ArchiveEventListeners } from "./preloadApi";

/**
 * 创建默认的压缩配置
 */
export function createDefaultArchiveConfig(): ArchiveConfig {
  return {
    compressionLevel: 0, // 仅存储，不压缩
    format: "7z",
    tempDir: require("os").tmpdir(),
    maxConcurrent: 2,
  };
}

/**
 * 设置事件转发
 * 将 ArchiveManager 的事件转发到渲染进程
 */
export function setupArchiveEventForwarding(archiveManager: ArchiveManager, mainWindow: BrowserWindow) {
  logger.info(`📦 [事件转发] 开始设置事件转发，ArchiveManager实例: ${archiveManager.constructor.name}`, { module: "archive" });

  // 任务创建事件
  archiveManager.on("task-created", (taskId, task) => {
    logger.info(`📦 [事件转发] 发送任务创建事件到渲染进程: ${taskId}`, { module: "archive" });
    mainWindow.webContents.send("archive-task-created", taskId, task);
  });

  // 任务进度事件
  archiveManager.on("task-progress", (taskId, progress, currentFile) => {
    logger.info(`📦 [事件转发] 发送进度事件到渲染进程: ${taskId} -> ${progress}% (${currentFile || ""})`, { module: "archive" });
    try {
      mainWindow.webContents.send("archive-task-progress", taskId, progress, currentFile);
      logger.info(`📦 [事件转发] 进度事件发送成功: ${taskId}`, { module: "archive" });
    } catch (error) {
      logger.error(`📦 [事件转发] 进度事件发送失败: ${taskId} - ${error}`, { module: "archive" });
    }
  });

  // 任务完成事件
  archiveManager.on("task-completed", (taskId, result) => {
    logger.info(`📦 [事件转发] 发送任务完成事件到渲染进程: ${taskId}`, { module: "archive" });
    mainWindow.webContents.send("archive-task-completed", taskId, result);
  });

  // 任务错误事件
  archiveManager.on("task-error", (taskId, error) => {
    logger.error(`📦 [事件转发] 发送任务错误事件到渲染进程: ${taskId} - ${error}`, { module: "archive" });
    mainWindow.webContents.send("archive-task-error", taskId, error);
  });

  // 任务取消事件
  archiveManager.on("task-cancelled", (taskId) => {
    logger.info(`📦 [事件转发] 发送任务取消事件到渲染进程: ${taskId}`, { module: "archive" });
    mainWindow.webContents.send("archive-task-cancelled", taskId);
  });

  logger.info(`📦 [事件转发] 事件转发设置完成，监听器数量: task-progress=${archiveManager.listenerCount("task-progress")}`, { module: "archive" });
}

/**
 * Archive 模块工厂函数
 * 用于在主进程中初始化完整的压缩系统
 */
export function createArchiveModule(config?: Partial<ArchiveConfig>) {
  const finalConfig = {
    ...createDefaultArchiveConfig(),
    ...config,
  };

  const archiveManager = new ArchiveManager(finalConfig);

  return {
    archiveManager,
    registerHandlers: () => registerArchiveIpcHandlers(archiveManager),
    unregisterHandlers: unregisterArchiveIpcHandlers,
    config: finalConfig,
  };
}

/**
 * 高级 Archive 模块初始化函数
 * 为主进程提供一键式压缩系统初始化
 *
 * @param mainWindow 主窗口实例，用于事件转发
 * @param config 可选的压缩配置
 * @returns Archive 模块实例和清理函数
 */
export function initializeArchiveModule(mainWindow: BrowserWindow, config?: Partial<ArchiveConfig>) {
  // 创建 Archive 模块
  const archiveModule = createArchiveModule(config);
  const { archiveManager } = archiveModule;

  // 注册 IPC 处理器
  archiveModule.registerHandlers();

  // 设置事件转发
  setupArchiveEventForwarding(archiveManager, mainWindow);

  // 返回模块实例和清理函数
  return {
    archiveManager,
    config: archiveModule.config,

    /**
     * 清理 Archive 模块资源
     */
    cleanup: () => {
      archiveModule.unregisterHandlers();
      archiveManager.removeAllListeners();
      // 清理所有未完成的任务
      archiveManager.cleanupCompletedTasks().catch(console.error);
    },
  };
}
